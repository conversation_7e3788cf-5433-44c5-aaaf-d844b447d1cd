from PyPDF2 import PdfReader
import docx
import re
import io

def parse_resume(file_name, file_bytes):
    """
    Parse resume from file bytes and extract structured information
    """
    text = ""

    try:
        if file_name.lower().endswith(".pdf"):
            # Create a BytesIO object from bytes for PDF reading
            pdf_file = io.BytesIO(file_bytes)
            pdf = PdfReader(pdf_file)
            for page in pdf.pages:
                text += page.extract_text() + "\n"

        elif file_name.lower().endswith(".docx"):
            # Create a BytesIO object from bytes for DOCX reading
            docx_file = io.BytesIO(file_bytes)
            doc = docx.Document(docx_file)
            for para in doc.paragraphs:
                text += para.text + "\n"

        else:
            # Try to decode as text file
            text = file_bytes.decode('utf-8', errors='ignore')

    except Exception as e:
        print(f"Error parsing file {file_name}: {e}")
        # Fallback: try to decode as text
        try:
            text = file_bytes.decode('utf-8', errors='ignore')
        except:
            text = "Error: Could not parse file content"

    return extract_fields(text)

def extract_fields(text):
    """
    Extract structured information from resume text
    """
    data = {}
    lines = text.split('\n')
    text_lower = text.lower()

    # Extract email
    email_match = re.search(r'[\w\.-]+@[\w\.-]+\.[\w]+', text)
    if email_match:
        data["email"] = email_match.group(0)

    # Extract name - improved logic
    name_found = False
    for i, line in enumerate(lines[:10]):  # Check first 10 lines
        line = line.strip()
        if line and len(line.split()) >= 2 and len(line) < 50:
            # Skip lines with common resume keywords
            skip_keywords = ['resume', 'cv', 'curriculum', 'vitae', 'profile', 'summary', 'objective',
                           'experience', 'education', 'skills', 'projects', 'achievements', 'contact',
                           'email', 'phone', 'address', 'linkedin', 'github']

            if not any(keyword in line.lower() for keyword in skip_keywords):
                # Check if it looks like a name (letters, spaces, maybe dots)
                if re.match(r'^[A-Za-z\s\.]+$', line) and not name_found:
                    data["name"] = line
                    name_found = True
                    break

    # Extract phone number - improved patterns for Indian numbers
    phone_patterns = [
        r'\+91[\s-]?\d{10}',  # Indian format with +91
        r'\+91[\s-]?\d{5}[\s-]?\d{5}',  # Indian format with +91 and space
        r'\d{10}',  # 10 digit Indian number
        r'\+?\d{1,3}[\s-]?\(?\d{3}\)?[\s-]?\d{3}[\s-]?\d{4}',  # International format
        r'\(?\d{3}\)?[\s-]?\d{3}[\s-]?\d{4}'  # US format
    ]
    for pattern in phone_patterns:
        phone_match = re.search(pattern, text)
        if phone_match:
            data["phone"] = phone_match.group(0)
            break

    # Extract LinkedIn URL
    linkedin_match = re.search(r'linkedin\.com/in/[\w-]+', text, re.IGNORECASE)
    if linkedin_match:
        data["linkedin"] = "https://" + linkedin_match.group(0)

    # Extract GitHub URL
    github_match = re.search(r'github\.com/[\w-]+', text, re.IGNORECASE)
    if github_match:
        data["github"] = "https://" + github_match.group(0)

    # Extract sections using improved section detection
    sections = extract_resume_sections(text, lines)

    # Extract technical skills from dedicated section or throughout text
    tech_skills = extract_technical_skills(text, sections)
    data["Technical Skills"] = tech_skills

    # Extract other sections
    data["work_experience"] = sections.get("work_experience", [])
    data["projects"] = sections.get("projects", [])
    data["soft_skills"] = sections.get("soft_skills", [])
    data["achievements"] = sections.get("achievements", [])
    data["position_of_responsibility"] = sections.get("position_of_responsibility", [])

    # Extract education (simple detection)
    education_keywords = ['university', 'college', 'institute', 'school', 'bachelor', 'master', 'phd', 'degree']
    education_lines = []
    for line in lines:
        if any(keyword in line.lower() for keyword in education_keywords):
            education_lines.append(line.strip())

    if education_lines:
        data["education"] = education_lines[:3]  # Limit to 3 entries

    # Extract work experience (simple detection)
    experience_keywords = ['experience', 'work', 'employment', 'position', 'role', 'job']
    experience_lines = []
    for line in lines:
        if any(keyword in line.lower() for keyword in experience_keywords) and len(line.strip()) > 10:
            experience_lines.append(line.strip())

    if experience_lines:
        data["experience"] = experience_lines[:5]  # Limit to 5 entries

    # Add parsing metadata
    data["parsing_notes"] = []
    if not data.get("email"):
        data["parsing_notes"].append("Email not found in resume")
    if not data.get("name"):
        data["parsing_notes"].append("Name not clearly identified")
    if not data.get("Technical Skills"):
        data["parsing_notes"].append("No technical skills detected")

    return data

def extract_resume_sections(text, lines):
    """
    Extract different sections from resume text
    """
    sections = {}
    current_section = None
    section_content = []

    # Define section headers and their variations
    section_headers = {
        'work_experience': ['work experience', 'professional experience', 'experience', 'employment', 'career history'],
        'projects': ['projects', 'project work', 'key projects', 'academic projects', 'personal projects'],
        'technical_skills': ['technical skills', 'tech stack', 'skills', 'technologies', 'programming languages'],
        'soft_skills': ['soft skills', 'interpersonal skills', 'personal skills', 'core competencies', 'key skills'],
        'achievements': ['achievements', 'accomplishments', 'awards', 'honors', 'recognition'],
        'position_of_responsibility': ['position of responsibility', 'leadership', 'responsibilities', 'positions held'],
        'education': ['education', 'academic background', 'qualifications', 'academic qualifications'],
        'certifications': ['certifications', 'certificates', 'professional certifications']
    }

    for line in lines:
        line_clean = line.strip()
        if not line_clean:
            continue

        # Check if this line is a section header
        line_lower = line_clean.lower()
        found_section = None

        for section_key, headers in section_headers.items():
            for header in headers:
                if header in line_lower and len(line_clean) < 50:  # Section headers are usually short
                    found_section = section_key
                    break
            if found_section:
                break

        if found_section:
            # Save previous section
            if current_section and section_content:
                sections[current_section] = [item.strip() for item in section_content if item.strip()]

            # Start new section
            current_section = found_section
            section_content = []
        elif current_section:
            # Add content to current section
            if line_clean:
                # Include all content, including bullet points
                section_content.append(line_clean)

    # Save last section
    if current_section and section_content:
        sections[current_section] = [item.strip() for item in section_content if item.strip()]

    return sections

def extract_technical_skills(text, sections):
    """
    Extract technical skills from dedicated section or throughout text
    """
    tech_skills = []

    # First try to get from dedicated technical skills section
    if 'technical_skills' in sections:
        skills_text = ' '.join(sections['technical_skills'])
        # Split by common delimiters
        skills_from_section = re.split(r'[,;|\n•\-◦▪]', skills_text)
        for skill in skills_from_section:
            skill = skill.strip()
            if skill and len(skill) < 30:  # Reasonable skill name length
                tech_skills.append(skill)

    # Also search for common technical skills throughout the text
    skill_keywords = [
        'Python', 'JavaScript', 'Java', 'C++', 'C#', 'React', 'Angular', 'Vue.js',
        'Node.js', 'Express', 'Django', 'Flask', 'FastAPI', 'Spring', 'Laravel',
        'HTML', 'CSS', 'SQL', 'MongoDB', 'PostgreSQL', 'MySQL', 'Redis',
        'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Git', 'Jenkins',
        'TensorFlow', 'PyTorch', 'Scikit-learn', 'Pandas', 'NumPy',
        'Machine Learning', 'Deep Learning', 'AI', 'Data Science',
        'REST API', 'GraphQL', 'Microservices', 'DevOps', 'CI/CD'
    ]

    text_lower = text.lower()
    for skill in skill_keywords:
        if skill.lower() in text_lower and skill not in tech_skills:
            tech_skills.append(skill)

    # Remove duplicates and limit to reasonable number
    return list(set(tech_skills))[:20]
