from PyPDF2 import PdfReader
import docx
import re
import io
import os
import json
from langchain_google_genai import ChatGoogleGenerativeAI
from prompts.parser_prompt import PROMPT

# Load environment variables from .env file
def load_env():
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"')
    except FileNotFoundError:
        pass
    except Exception:
        pass

# Load environment variables
load_env()

# Initialize Google AI model
try:
    llm = ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        google_api_key=os.getenv("GOOGLE_API_KEY"),
        temperature=0.1
    )
except Exception as e:
    print(f"Warning: Google AI model initialization failed: {e}")
    llm = None

def extract_text_from_file(file_bytes, file_name):
    """
    Extract text content from file bytes
    """
    text = ""

    try:
        if file_name.lower().endswith(".pdf"):
            # Enhanced PDF extraction with better layout handling
            pdf_file = io.BytesIO(file_bytes)
            pdf = PdfReader(pdf_file)

            for page_num, page in enumerate(pdf.pages):
                # Extract text with better formatting
                page_text = page.extract_text()

                # Try to extract hyperlinks and annotations for LinkedIn/GitHub
                try:
                    if '/Annots' in page:
                        annotations = page['/Annots']
                        if annotations:
                            for annotation in annotations:
                                try:
                                    annotation_obj = annotation.get_object()
                                    if annotation_obj.get('/Subtype') == '/Link':
                                        # Extract URL from link annotation
                                        if '/A' in annotation_obj:
                                            action = annotation_obj['/A']
                                            if '/URI' in action:
                                                uri = str(action['/URI'])
                                                # Add the URL to the text for parsing
                                                if 'linkedin' in uri.lower() or 'github' in uri.lower():
                                                    page_text += f"\nExtracted Link: {uri}\n"
                                except Exception:
                                    continue
                except Exception:
                    pass

                # Improve text formatting for better parsing
                page_text = improve_pdf_text_formatting(page_text)
                # Clean up fragmented text
                page_text = clean_fragmented_text(page_text)
                text += page_text + "\n"

        elif file_name.lower().endswith(".docx"):
            # Enhanced DOCX extraction with hyperlink detection
            docx_file = io.BytesIO(file_bytes)
            doc = docx.Document(docx_file)

            # Extract text from paragraphs
            for para in doc.paragraphs:
                para_text = para.text

                # Extract hyperlinks from the paragraph
                for run in para.runs:
                    if run.element.tag.endswith('hyperlink') or 'hyperlink' in str(run.element.xml):
                        # Try to extract hyperlink URL
                        try:
                            hyperlink_id = run.element.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}id')
                            if hyperlink_id:
                                # Get the relationship to find the actual URL
                                rel = doc.part.rels[hyperlink_id]
                                url = rel.target_ref
                                if 'linkedin' in url.lower() or 'github' in url.lower():
                                    para_text += f" [Link: {url}]"
                        except Exception:
                            pass

                # Check for hyperlinks in a different way
                for hyperlink in para._element.xpath('.//w:hyperlink'):
                    try:
                        hyperlink_id = hyperlink.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}id')
                        if hyperlink_id and hyperlink_id in doc.part.rels:
                            url = doc.part.rels[hyperlink_id].target_ref
                            if 'linkedin' in url.lower() or 'github' in url.lower():
                                para_text += f" [Link: {url}]"
                    except Exception:
                        pass

                text += para_text + "\n"

            # Also extract text from tables (contact info often in tables)
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            text += cell_text + "\n"

            # Apply formatting improvements
            text = improve_pdf_text_formatting(text)

        else:
            # Try to decode as text file
            text = file_bytes.decode('utf-8', errors='ignore')

    except Exception as e:
        print(f"Error parsing file {file_name}: {e}")
        # Fallback: try to decode as text
        try:
            text = file_bytes.decode('utf-8', errors='ignore')
        except:
            text = "Error: Could not parse file content"

    return text

def improve_pdf_text_formatting(text):
    """Improve PDF text formatting for better contact info and link extraction"""
    import re

    # Add line breaks before common section headers
    section_headers = [
        'EXPERIENCE', 'EDUCATION', 'SKILLS', 'PROJECTS', 'ACHIEVEMENTS',
        'CERTIFICATIONS', 'CONTACT', 'SUMMARY', 'OBJECTIVE', 'WORK EXPERIENCE',
        'TECHNICAL SKILLS', 'PROFESSIONAL EXPERIENCE', 'ACADEMIC PROJECTS',
        'PERSONAL PROJECTS', 'INTERNSHIPS', 'TRAINING', 'AWARDS'
    ]

    for header in section_headers:
        # Add line breaks before section headers (case insensitive)
        text = re.sub(f'\\b({header})\\b', r'\n\1\n', text, flags=re.IGNORECASE)

    # Enhanced email detection (common in top-right of resumes)
    email_patterns = [
        r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        r'Email\s*:?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        r'E-mail\s*:?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
    ]

    for pattern in email_patterns:
        emails = re.findall(pattern, text, re.IGNORECASE)
        for email in emails:
            if '@' in email:  # Ensure it's actually an email
                text = text.replace(email, f'\nEmail: {email}\n')

    # Enhanced phone number detection (common in top-right)
    phone_patterns = [
        r'(\+91[-\s]?\d{5}[-\s]?\d{5})',  # Indian format with +91
        r'(\+91[-\s]?\d{10})',           # Indian format compact
        r'(91[-\s]?\d{10})',             # Indian without +
        r'(\d{10})',                     # 10-digit number
        r'(\d{3}[-\s]?\d{3}[-\s]?\d{4})', # US format
        r'Phone\s*:?\s*(\+?91?[-\s]?\d{10})',  # With Phone label
        r'Mobile\s*:?\s*(\+?91?[-\s]?\d{10})', # With Mobile label
        r'Contact\s*:?\s*(\+?91?[-\s]?\d{10})' # With Contact label
    ]

    for pattern in phone_patterns:
        phones = re.findall(pattern, text, re.IGNORECASE)
        for phone in phones:
            # Clean phone number and validate length
            clean_phone = re.sub(r'[^\d+]', '', phone)
            if len(clean_phone) >= 10:
                text = text.replace(phone, f'\nPhone: {phone}\n')

    # Enhanced LinkedIn profile detection (including hover links)
    linkedin_patterns = [
        r'(linkedin\.com/in/[a-zA-Z0-9-]+/?)',
        r'(www\.linkedin\.com/in/[a-zA-Z0-9-]+/?)',
        r'(https?://(?:www\.)?linkedin\.com/in/[a-zA-Z0-9-]+/?)',
        r'LinkedIn\s*:?\s*(linkedin\.com/in/[a-zA-Z0-9-]+/?)',
        r'LinkedIn\s*:?\s*(www\.linkedin\.com/in/[a-zA-Z0-9-]+/?)',
        r'LinkedIn\s*:?\s*(https?://(?:www\.)?linkedin\.com/in/[a-zA-Z0-9-]+/?)'
    ]

    for pattern in linkedin_patterns:
        linkedin_urls = re.findall(pattern, text, re.IGNORECASE)
        for url in linkedin_urls:
            # Ensure proper URL format
            if not url.startswith('http'):
                url = f'https://{url}'
            text = text.replace(url.replace('https://', ''), f'\nLinkedIn: {url}\n')

    # Enhanced GitHub profile detection (including hover links)
    github_patterns = [
        r'(github\.com/[a-zA-Z0-9-]+/?)',
        r'(www\.github\.com/[a-zA-Z0-9-]+/?)',
        r'(https?://(?:www\.)?github\.com/[a-zA-Z0-9-]+/?)',
        r'GitHub\s*:?\s*(github\.com/[a-zA-Z0-9-]+/?)',
        r'GitHub\s*:?\s*(www\.github\.com/[a-zA-Z0-9-]+/?)',
        r'GitHub\s*:?\s*(https?://(?:www\.)?github\.com/[a-zA-Z0-9-]+/?)',
        r'Github\s*:?\s*(github\.com/[a-zA-Z0-9-]+/?)'  # Common misspelling
    ]

    for pattern in github_patterns:
        github_urls = re.findall(pattern, text, re.IGNORECASE)
        for url in github_urls:
            # Ensure proper URL format
            if not url.startswith('http'):
                url = f'https://{url}'
            text = text.replace(url.replace('https://', ''), f'\nGitHub: {url}\n')

    # Detect location information (often in header)
    location_patterns = [
        r'Location\s*:?\s*([A-Za-z\s,]+(?:India|IN))',
        r'Address\s*:?\s*([A-Za-z\s,]+(?:India|IN))',
        r'([A-Za-z]+,\s*[A-Za-z]+,?\s*India)',
        r'([A-Za-z]+,\s*India)'
    ]

    for pattern in location_patterns:
        locations = re.findall(pattern, text, re.IGNORECASE)
        for location in locations:
            text = text.replace(location, f'\nLocation: {location}\n')

    # Clean up multiple newlines
    text = re.sub(r'\n\s*\n', '\n\n', text)

    return text

def parse_resume(file_name, file_bytes):
    """
    Parse resume from file bytes using LLM with updated prompt
    """
    # Extract text from file
    text = extract_text_from_file(file_bytes, file_name)

    # Use LLM-based parsing if available
    if llm:
        try:
            return parse_with_llm(text)
        except Exception as e:
            print(f"LLM parsing failed: {e}, falling back to regex parsing")
            return extract_fields(text)
    else:
        # Fallback to regex-based parsing
        return extract_fields(text)

def parse_with_llm(resume_text):
    """
    Parse resume using LLM with the updated prompt
    """
    # Create the full prompt with the resume text
    full_prompt = f"{PROMPT}\n\nRESUME TEXT TO PARSE:\n{resume_text}\n\nPlease extract the information and return ONLY the JSON object as specified in the prompt:"

    # Get response from LLM
    response = llm.invoke(full_prompt)

    # Extract JSON from response
    response_text = response.content if hasattr(response, 'content') else str(response)

    # Try to extract JSON from the response
    try:
        # Look for JSON in the response
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1

        if json_start != -1 and json_end > json_start:
            json_str = response_text[json_start:json_end]
            parsed_data = json.loads(json_str)

            # Flatten the structure to match expected format
            flattened_data = {}

            # Extract personal info
            personal_info = parsed_data.get('personal_info', {})
            flattened_data['name'] = personal_info.get('name')
            flattened_data['email'] = personal_info.get('email')
            flattened_data['phone'] = personal_info.get('phone')
            flattened_data['linkedin'] = personal_info.get('linkedin')
            flattened_data['github'] = personal_info.get('github')
            flattened_data['location'] = personal_info.get('location')

            # Extract professional info
            professional_info = parsed_data.get('professional_info', {})
            flattened_data['summary'] = professional_info.get('summary')
            flattened_data['total_years_experience'] = professional_info.get('total_years_experience')
            flattened_data['experience_level'] = professional_info.get('experience_level')
            flattened_data['primary_skill_domain'] = professional_info.get('primary_skill_domain')
            flattened_data['target_job_titles'] = professional_info.get('target_job_titles', [])

            # Extract other sections
            flattened_data['education'] = parsed_data.get('education', [])
            flattened_data['work_experience'] = parsed_data.get('work_experience', [])
            flattened_data['projects'] = parsed_data.get('projects', [])
            flattened_data['technical_skills'] = parsed_data.get('technical_skills', {})
            flattened_data['soft_skills'] = parsed_data.get('soft_skills', [])
            flattened_data['achievements'] = parsed_data.get('achievements', [])
            flattened_data['publications'] = parsed_data.get('publications', [])
            flattened_data['positions_of_responsibility'] = parsed_data.get('positions_of_responsibility', [])
            flattened_data['coursework'] = parsed_data.get('coursework', [])
            flattened_data['certifications'] = parsed_data.get('certifications', [])
            flattened_data['languages'] = parsed_data.get('languages', [])

            # Add Technical Skills as a flat list for backward compatibility
            tech_skills = parsed_data.get('technical_skills', {})
            all_tech_skills = []
            for category, skills in tech_skills.items():
                if isinstance(skills, list):
                    all_tech_skills.extend(skills)
            flattened_data['Technical Skills'] = all_tech_skills

            return flattened_data

        else:
            raise ValueError("No valid JSON found in response")

    except Exception as e:
        print(f"Error parsing LLM response: {e}")
        print(f"Response was: {response_text[:500]}...")
        # Fallback to regex parsing
        return extract_fields(resume_text)

def extract_fields(text):
    """
    Extract structured information from resume text
    """
    data = {}
    lines = text.split('\n')
    text_lower = text.lower()

    # Extract email
    email_match = re.search(r'[\w\.-]+@[\w\.-]+\.[\w]+', text)
    if email_match:
        data["email"] = email_match.group(0)

    # Extract name - improved logic
    name_found = False
    for i, line in enumerate(lines[:10]):  # Check first 10 lines
        line = line.strip()
        if line and len(line.split()) >= 2 and len(line) < 50:
            # Skip lines with common resume keywords
            skip_keywords = ['resume', 'cv', 'curriculum', 'vitae', 'profile', 'summary', 'objective',
                           'experience', 'education', 'skills', 'projects', 'achievements', 'contact',
                           'email', 'phone', 'address', 'linkedin', 'github']

            if not any(keyword in line.lower() for keyword in skip_keywords):
                # Check if it looks like a name (letters, spaces, maybe dots)
                if re.match(r'^[A-Za-z\s\.]+$', line) and not name_found:
                    data["name"] = line
                    name_found = True
                    break

    # Extract phone number - improved patterns for Indian numbers
    phone_patterns = [
        r'\+91[\s-]?\d{10}',  # Indian format with +91
        r'\+91[\s-]?\d{5}[\s-]?\d{5}',  # Indian format with +91 and space
        r'\d{10}',  # 10 digit Indian number
        r'\+?\d{1,3}[\s-]?\(?\d{3}\)?[\s-]?\d{3}[\s-]?\d{4}',  # International format
        r'\(?\d{3}\)?[\s-]?\d{3}[\s-]?\d{4}'  # US format
    ]
    for pattern in phone_patterns:
        phone_match = re.search(pattern, text)
        if phone_match:
            data["phone"] = phone_match.group(0)
            break

    # Extract LinkedIn URL
    linkedin_match = re.search(r'linkedin\.com/in/[\w-]+', text, re.IGNORECASE)
    if linkedin_match:
        data["linkedin"] = "https://" + linkedin_match.group(0)

    # Extract GitHub URL
    github_match = re.search(r'github\.com/[\w-]+', text, re.IGNORECASE)
    if github_match:
        data["github"] = "https://" + github_match.group(0)

    # Extract sections using improved section detection
    sections = extract_resume_sections(text, lines)

    # Extract technical skills from dedicated section or throughout text
    tech_skills = extract_technical_skills(text, sections)
    data["Technical Skills"] = tech_skills

    # Extract other sections
    data["work_experience"] = sections.get("work_experience", [])
    data["projects"] = sections.get("projects", [])
    data["soft_skills"] = sections.get("soft_skills", [])
    data["achievements"] = sections.get("achievements", [])
    data["position_of_responsibility"] = sections.get("position_of_responsibility", [])

    # Extract education (simple detection)
    education_keywords = ['university', 'college', 'institute', 'school', 'bachelor', 'master', 'phd', 'degree']
    education_lines = []
    for line in lines:
        if any(keyword in line.lower() for keyword in education_keywords):
            education_lines.append(line.strip())

    if education_lines:
        data["education"] = education_lines[:3]  # Limit to 3 entries

    # Extract work experience (simple detection)
    experience_keywords = ['experience', 'work', 'employment', 'position', 'role', 'job']
    experience_lines = []
    for line in lines:
        if any(keyword in line.lower() for keyword in experience_keywords) and len(line.strip()) > 10:
            experience_lines.append(line.strip())

    if experience_lines:
        data["experience"] = experience_lines[:5]  # Limit to 5 entries

    # Add parsing metadata
    data["parsing_notes"] = []
    if not data.get("email"):
        data["parsing_notes"].append("Email not found in resume")
    if not data.get("name"):
        data["parsing_notes"].append("Name not clearly identified")
    if not data.get("Technical Skills"):
        data["parsing_notes"].append("No technical skills detected")

    return data

def extract_resume_sections(text, lines):
    """
    Extract different sections from resume text
    """
    sections = {}
    current_section = None
    section_content = []

    # Define section headers and their variations
    section_headers = {
        'work_experience': ['work experience', 'professional experience', 'experience', 'employment', 'career history'],
        'projects': ['projects', 'project work', 'key projects', 'academic projects', 'personal projects'],
        'technical_skills': ['technical skills', 'tech stack', 'skills', 'technologies', 'programming languages'],
        'soft_skills': ['soft skills', 'interpersonal skills', 'personal skills', 'core competencies', 'key skills'],
        'achievements': ['achievements', 'accomplishments', 'awards', 'honors', 'recognition'],
        'position_of_responsibility': ['position of responsibility', 'leadership', 'responsibilities', 'positions held'],
        'education': ['education', 'academic background', 'qualifications', 'academic qualifications'],
        'certifications': ['certifications', 'certificates', 'professional certifications']
    }

    for line in lines:
        line_clean = line.strip()
        if not line_clean:
            continue

        # Check if this line is a section header
        line_lower = line_clean.lower()
        found_section = None

        for section_key, headers in section_headers.items():
            for header in headers:
                if header in line_lower and len(line_clean) < 50:  # Section headers are usually short
                    found_section = section_key
                    break
            if found_section:
                break

        if found_section:
            # Save previous section
            if current_section and section_content:
                sections[current_section] = [item.strip() for item in section_content if item.strip()]

            # Start new section
            current_section = found_section
            section_content = []
        elif current_section:
            # Add content to current section
            if line_clean:
                # Include all content, including bullet points
                section_content.append(line_clean)

    # Save last section
    if current_section and section_content:
        sections[current_section] = [item.strip() for item in section_content if item.strip()]

    return sections

def extract_technical_skills(text, sections):
    """
    Extract technical skills from dedicated section or throughout text
    """
    tech_skills = []

    # First try to get from dedicated technical skills section
    if 'technical_skills' in sections:
        skills_text = ' '.join(sections['technical_skills'])
        # Split by common delimiters
        skills_from_section = re.split(r'[,;|\n•\-◦▪]', skills_text)
        for skill in skills_from_section:
            skill = skill.strip()
            if skill and len(skill) < 30:  # Reasonable skill name length
                tech_skills.append(skill)

    # Also search for common technical skills throughout the text
    skill_keywords = [
        'Python', 'JavaScript', 'Java', 'C++', 'C#', 'React', 'Angular', 'Vue.js',
        'Node.js', 'Express', 'Django', 'Flask', 'FastAPI', 'Spring', 'Laravel',
        'HTML', 'CSS', 'SQL', 'MongoDB', 'PostgreSQL', 'MySQL', 'Redis',
        'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Git', 'Jenkins',
        'TensorFlow', 'PyTorch', 'Scikit-learn', 'Pandas', 'NumPy',
        'Machine Learning', 'Deep Learning', 'AI', 'Data Science',
        'REST API', 'GraphQL', 'Microservices', 'DevOps', 'CI/CD'
    ]

    text_lower = text.lower()
    for skill in skill_keywords:
        if skill.lower() in text_lower and skill not in tech_skills:
            tech_skills.append(skill)

    # Remove duplicates and limit to reasonable number
    return list(set(tech_skills))[:20]

def clean_fragmented_text(text):
    """Clean up fragmented text from PDF extraction"""
    import re

    # Fix fragmented phone numbers
    # Pattern: Phone: + Phone: 91- Phone: 7376486738
    text = re.sub(r'Phone:\s*\+\s*Phone:\s*91-?\s*Phone:\s*(\d+)', r'Phone: +91\1', text)
    text = re.sub(r'Phone:\s*(\+91[-\s]?\d+)', r'Phone: \1', text)

    # Fix fragmented emails
    text = re.sub(r'Email:\s*Email:\s*([^\s]+@[^\s]+)', r'Email: \1', text)

    # Fix fragmented LinkedIn/GitHub
    text = re.sub(r'LinkedIn\s*/\w+\s*GitHub', 'LinkedIn GitHub', text)

    # Clean up multiple spaces and newlines
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\n\s*\n', '\n\n', text)

    # Fix location fragmentation
    text = re.sub(r'Location:\s*([^,]+),?\s*Location:\s*([^,]+)', r'Location: \1, \2', text)

    return text
