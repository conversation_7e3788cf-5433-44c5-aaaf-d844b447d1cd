#!/usr/bin/env python3
"""
Data validation utilities to ensure data flows correctly through the system
"""

import json
from typing import Dict, List, Any, Optional, Tuple

class ResumeDataValidator:
    """Validates and ensures resume data integrity throughout the system"""
    
    REQUIRED_FIELDS = ['name', 'Technical Skills']
    OPTIONAL_FIELDS = ['email', 'phone', 'linkedin', 'github', 'experience', 'education',
                      'work_experience', 'projects', 'soft_skills', 'achievements',
                      'position_of_responsibility']
    
    @staticmethod
    def validate_parsed_resume(data: Dict[str, Any]) -> Tuple[bool, List[str], Dict[str, Any]]:
        """
        Validate parsed resume data and return validation status, issues, and cleaned data
        
        Returns:
            (is_valid, issues_list, cleaned_data)
        """
        issues = []
        cleaned_data = {}
        
        if not isinstance(data, dict):
            return False, ["Data is not a dictionary"], {}
        
        # Check required fields
        for field in ResumeDataValidator.REQUIRED_FIELDS:
            if field not in data or not data[field]:
                issues.append(f"Missing required field: {field}")
            else:
                cleaned_data[field] = data[field]
        
        # Process optional fields
        for field in ResumeDataValidator.OPTIONAL_FIELDS:
            if field in data and data[field]:
                cleaned_data[field] = data[field]
        
        # Validate Technical Skills specifically
        if 'Technical Skills' in data:
            skills = data['Technical Skills']
            if isinstance(skills, list) and len(skills) > 0:
                # Clean and deduplicate skills
                cleaned_skills = list(set([skill.strip() for skill in skills if skill.strip()]))
                cleaned_data['Technical Skills'] = cleaned_skills
            else:
                issues.append("Technical Skills must be a non-empty list")
        
        # Ensure experience and education are lists
        for field in ['experience', 'education']:
            if field in cleaned_data:
                if not isinstance(cleaned_data[field], list):
                    cleaned_data[field] = [str(cleaned_data[field])]
        
        # Add metadata
        import datetime
        cleaned_data['validation_timestamp'] = str(datetime.datetime.now())
        cleaned_data['data_quality_score'] = ResumeDataValidator._calculate_quality_score(cleaned_data)
        
        is_valid = len(issues) == 0 or (len([i for i in issues if "required" in i.lower()]) == 0)
        
        return is_valid, issues, cleaned_data
    
    @staticmethod
    def _calculate_quality_score(data: Dict[str, Any]) -> int:
        """Calculate data quality score (0-100)"""
        score = 0
        
        # Required fields (40 points)
        if data.get('name'):
            score += 20
        if data.get('Technical Skills') and len(data['Technical Skills']) > 0:
            score += 20
        
        # Optional but important fields (60 points)
        if data.get('email'):
            score += 10
        if data.get('phone'):
            score += 8
        if data.get('work_experience') and len(data['work_experience']) > 0:
            score += 12
        if data.get('projects') and len(data['projects']) > 0:
            score += 10
        if data.get('education') and len(data['education']) > 0:
            score += 8
        if data.get('achievements') and len(data['achievements']) > 0:
            score += 6
        if data.get('soft_skills') and len(data['soft_skills']) > 0:
            score += 3
        if data.get('position_of_responsibility') and len(data['position_of_responsibility']) > 0:
            score += 3
        
        return min(score, 100)
    
    @staticmethod
    def validate_job_data(jobs: List[Dict[str, Any]]) -> Tuple[bool, List[str], List[Dict[str, Any]]]:
        """
        Validate job recommendation data
        
        Returns:
            (is_valid, issues_list, cleaned_jobs)
        """
        issues = []
        cleaned_jobs = []
        
        if not isinstance(jobs, list):
            return False, ["Jobs data is not a list"], []
        
        required_job_fields = ['title', 'company', 'apply_url']
        
        for i, job in enumerate(jobs):
            if not isinstance(job, dict):
                issues.append(f"Job {i+1} is not a dictionary")
                continue
            
            cleaned_job = {}
            job_issues = []
            
            # Check required fields
            for field in required_job_fields:
                if field not in job or not job[field]:
                    job_issues.append(f"Job {i+1} missing {field}")
                else:
                    cleaned_job[field] = job[field]
            
            # Add optional fields with defaults
            cleaned_job['location'] = job.get('location', 'Location not specified')
            cleaned_job['salary'] = job.get('salary', 'Salary not specified')
            cleaned_job['relevance_score'] = job.get('relevance_score', 50)
            cleaned_job['source'] = job.get('source', 'Unknown')
            cleaned_job['url'] = job.get('url', job.get('apply_url', ''))
            cleaned_job['content'] = job.get('content', 'Job description not available')
            
            # Validate apply_url
            if 'apply_url' in cleaned_job:
                if not cleaned_job['apply_url'].startswith(('http://', 'https://')):
                    job_issues.append(f"Job {i+1} has invalid apply_url")
            
            if len(job_issues) == 0:
                cleaned_jobs.append(cleaned_job)
            else:
                issues.extend(job_issues)
        
        is_valid = len(cleaned_jobs) > 0
        return is_valid, issues, cleaned_jobs
    
    @staticmethod
    def validate_session_data(session_data: Dict[str, Any]) -> Tuple[bool, List[str], Dict[str, Any]]:
        """
        Validate complete session data
        
        Returns:
            (is_valid, issues_list, cleaned_session_data)
        """
        issues = []
        cleaned_session = {}
        
        # Validate session ID
        if 'session_id' not in session_data or not session_data['session_id']:
            issues.append("Missing session_id")
        else:
            cleaned_session['session_id'] = session_data['session_id']
        
        # Validate resume data
        if 'resume_data' in session_data:
            resume_valid, resume_issues, cleaned_resume = ResumeDataValidator.validate_parsed_resume(
                session_data['resume_data']
            )
            if resume_valid:
                cleaned_session['resume_data'] = cleaned_resume
            issues.extend(resume_issues)
        else:
            issues.append("Missing resume_data")
        
        # Validate jobs if present
        if 'jobs' in session_data:
            jobs_valid, job_issues, cleaned_jobs = ResumeDataValidator.validate_job_data(
                session_data['jobs']
            )
            if jobs_valid:
                cleaned_session['jobs'] = cleaned_jobs
            issues.extend(job_issues)
        
        # Add other session fields
        for field in ['improvements', 'message', 'user_name', 'email']:
            if field in session_data:
                cleaned_session[field] = session_data[field]
        
        is_valid = 'resume_data' in cleaned_session and 'session_id' in cleaned_session
        return is_valid, issues, cleaned_session
    
    @staticmethod
    def ensure_json_serializable(data: Any) -> Any:
        """Ensure data is JSON serializable"""
        try:
            json.dumps(data)
            return data
        except (TypeError, ValueError):
            # Convert non-serializable objects
            if isinstance(data, dict):
                return {k: ResumeDataValidator.ensure_json_serializable(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [ResumeDataValidator.ensure_json_serializable(item) for item in data]
            elif hasattr(data, '__dict__'):
                return str(data)
            else:
                return str(data)
    
    @staticmethod
    def create_fallback_data(file_name: str = "unknown") -> Dict[str, Any]:
        """Create fallback resume data when parsing fails"""
        return {
            "name": file_name.split('.')[0].replace('_', ' ').title() if file_name != "unknown" else "Demo User",
            "email": "<EMAIL>",
            "Technical Skills": ["Python", "JavaScript", "React", "Node.js", "SQL"],
            "experience": ["Software Engineer"],
            "education": ["Computer Science Degree"],
            "data_quality_score": 30,
            "is_fallback": True,
            "validation_timestamp": "fallback_created"
        }

def validate_and_clean_data(data: Dict[str, Any], data_type: str = "resume") -> Dict[str, Any]:
    """
    Main validation function - validates and cleans data based on type
    
    Args:
        data: Data to validate
        data_type: Type of data ("resume", "jobs", "session")
    
    Returns:
        Cleaned and validated data
    """
    if data_type == "resume":
        is_valid, issues, cleaned_data = ResumeDataValidator.validate_parsed_resume(data)
    elif data_type == "jobs":
        is_valid, issues, cleaned_data = ResumeDataValidator.validate_job_data(data)
    elif data_type == "session":
        is_valid, issues, cleaned_data = ResumeDataValidator.validate_session_data(data)
    else:
        return data
    
    # Log issues if any
    if issues:
        print(f"⚠️ Data validation issues for {data_type}: {issues}")
    
    # Ensure JSON serializable
    cleaned_data = ResumeDataValidator.ensure_json_serializable(cleaned_data)
    
    return cleaned_data if is_valid else (ResumeDataValidator.create_fallback_data() if data_type == "resume" else {})
