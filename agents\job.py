from langchain.agents import Tool
from prompts.job_prompt import <PERSON>OMPT
import os
from tavily import TavilyClient
from langchain_google_genai import ChatGoogleGenerativeAI
import json

# Load environment variables from .env file
def load_env():
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"')
    except FileNotFoundError:
        pass
    except Exception as e:
        pass

# Load environment variables
load_env()

# Initialize Tavily client with error handling
try:
    tavily_key = os.getenv("TAVILY_API_KEY")
    if tavily_key:
        client = TavilyClient(api_key=tavily_key)
        print("✅ Tavily client initialized successfully")
    else:
        print("⚠️ TAVILY_API_KEY not found in environment variables")
        client = None
except Exception as e:
    print(f"⚠️ Tavily client initialization failed: {e}")
    client = None

# Initialize Google AI model for job search
try:
    llm = ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        google_api_key=os.getenv("GOOGLE_API_KEY"),
        temperature=0.3
    )
except Exception as e:
    print(f"Warning: Google AI model initialization failed: {e}")
    llm = None

def search_jobs_with_llm(parsed_resume):
    """LLM-powered startup-focused job search for India"""
    if not llm:
        print("⚠️ LLM not available, falling back to basic search")
        return search_jobs_basic(parsed_resume)

    try:
        # Create the job search prompt with resume data
        resume_summary = f"""
        Name: {parsed_resume.get('name', 'N/A')}
        Experience Level: {parsed_resume.get('experience_level', 'Entry')}
        Years of Experience: {parsed_resume.get('total_years_experience', 0)}
        Primary Skills: {', '.join(parsed_resume.get('Technical Skills', [])[:5])}
        Location: {parsed_resume.get('location', 'India')}
        Target Job Titles: {', '.join(parsed_resume.get('target_job_titles', []))}
        """

        full_prompt = f"{PROMPT}\n\nRESUME DATA:\n{resume_summary}\n\nPlease find relevant startup jobs in India for this candidate. Focus on the specific Indian cities mentioned in the prompt and prioritize LinkedIn company pages and career sites."

        # Get response from LLM
        response = llm.invoke(full_prompt)
        response_text = response.content if hasattr(response, 'content') else str(response)

        # For now, let's use the LLM to generate better search queries and then use Tavily
        # Extract search queries from LLM response and use them with Tavily
        search_queries = extract_search_queries_from_llm_response(response_text, parsed_resume)

        # Use Tavily with the LLM-generated queries
        return search_jobs_with_tavily(search_queries, parsed_resume)

    except Exception as e:
        print(f"⚠️ LLM job search failed: {e}, falling back to basic search")
        return search_jobs_basic(parsed_resume)

def extract_search_queries_from_llm_response(response_text, parsed_resume):
    """Extract and generate better search queries based on LLM response"""
    tech_skills = parsed_resume.get('Technical Skills', [])
    experience_level = parsed_resume.get('experience_level', 'Entry').lower()

    # Generate India-focused startup search queries
    search_queries = []

    # Indian cities
    indian_cities = ['Noida', 'Mumbai', 'Gurgaon', 'Bengaluru', 'Pune', 'Hyderabad', 'Lucknow', 'Indore', 'Ahmedabad', 'Chennai', 'Kolkata']

    # Primary skills for job search
    primary_skills = tech_skills[:3] if tech_skills else ['Software Engineer']

    # LinkedIn company page searches for startups
    for city in indian_cities[:5]:  # Top 5 cities
        for skill in primary_skills[:2]:  # Top 2 skills
            search_queries.extend([
                f"{skill} startup {city} site:linkedin.com/company",
                f"tech startup {city} jobs site:linkedin.com",
                f"{skill} engineer {city} site:linkedin.com/jobs"
            ])

    # Career page searches
    startup_companies = [
        'razorpay', 'swiggy', 'zomato', 'paytm', 'flipkart', 'ola', 'byju',
        'unacademy', 'vedantu', 'cred', 'phonepe', 'meesho', 'sharechat'
    ]

    for company in startup_companies[:5]:
        search_queries.extend([
            f"careers.{company}.com {primary_skills[0]} India",
            f"{company} jobs {primary_skills[0]} site:linkedin.com"
        ])

    # AngelList searches
    for skill in primary_skills[:2]:
        search_queries.extend([
            f"{skill} engineer India site:angel.co",
            f"{skill} developer startup India site:wellfound.com"
        ])

    return search_queries[:15]  # Limit to 15 queries

def search_jobs(parsed_resume):
    """Main job search function - uses LLM-powered search"""
    return search_jobs_with_llm(parsed_resume)

def search_jobs_basic(parsed_resume):
    """Fallback basic job search function"""
    tech_stack = parsed_resume.get("Technical Skills", [])
    experience = parsed_resume.get("experience", [])
    education = parsed_resume.get("education", [])

    # Determine experience level
    experience_level = "entry" if len(experience) <= 1 else "mid" if len(experience) <= 3 else "senior"

    # Build India-focused search queries
    search_queries = []

    # Indian cities
    indian_cities = ['Noida', 'Mumbai', 'Gurgaon', 'Bengaluru', 'Pune', 'Hyderabad']

    # Primary tech stack searches with India focus
    if tech_stack:
        primary_skills = tech_stack[:3]  # Top 3 skills
        for city in indian_cities[:3]:  # Top 3 cities
            search_queries.extend([
                f"{primary_skills[0]} jobs {city} site:linkedin.com",
                f"{primary_skills[0]} engineer {city} site:naukri.com",
                f"{primary_skills[0]} developer {city} startup"
            ])

    # Indian startup companies
    indian_startups = [
        "Razorpay", "Swiggy", "Zomato", "Paytm", "Flipkart", "Ola", "BYJU'S",
        "Unacademy", "Vedantu", "CRED", "PhonePe", "Meesho", "ShareChat",
        "Freshworks", "Zoho", "InMobi", "Hike", "Practo", "UrbanClap", "Grofers"
    ]

    if tech_stack:
        for company in indian_startups[:5]:  # Search top 5 Indian startups
            search_queries.append(f"{tech_stack[0]} jobs {company} India careers")

    # Industry-specific searches with India focus
    if any(skill.lower() in ['python', 'java', 'javascript', 'react', 'node'] for skill in tech_stack):
        search_queries.extend([
            "software engineer jobs fintech India",
            "full stack developer startup jobs Bangalore",
            "backend developer remote jobs India",
            "frontend developer jobs Mumbai"
        ])

    if any(skill.lower() in ['data', 'machine learning', 'ai', 'analytics'] for skill in tech_stack):
        search_queries.extend([
            "data scientist jobs Bangalore",
            "machine learning engineer jobs India",
            "AI researcher positions Hyderabad",
            "data analyst remote jobs India"
        ])

    if any(skill.lower() in ['devops', 'aws', 'docker', 'kubernetes'] for skill in tech_stack):
        search_queries.extend([
            "devops engineer jobs Pune",
            "cloud engineer positions India",
            "site reliability engineer jobs Gurgaon"
        ])

    return search_jobs_with_tavily(search_queries, parsed_resume)

def search_jobs_with_tavily(search_queries, parsed_resume):
    """Execute job search using Tavily with given queries"""

    all_jobs = []

    if not client:
        # Enhanced mock job data based on skills with India focus
        mock_jobs = generate_india_focused_mock_jobs(parsed_resume.get('Technical Skills', []), parsed_resume.get('experience_level', 'Entry'))
        return mock_jobs

    try:
        # Execute multiple searches for comprehensive results
        for query in search_queries[:8]:  # Limit to 8 queries to avoid rate limits
            try:
                result = client.search(query=query, max_results=3)
                jobs = result.get("results", [])

                # Process and enhance job data
                for job in jobs:
                    job_url = job.get("url", "")
                    enhanced_job = {
                        "title": job.get("title", "Software Position"),
                        "url": job_url,
                        "apply_url": extract_apply_url(job_url, job.get("content", "")),
                        "content": job.get("content", "")[:200] + "...",
                        "company": extract_company_from_url(job_url),
                        "source": extract_source_from_url(job_url),
                        "location": extract_location_from_content(job.get("content", "")),
                        "salary": extract_salary_from_content(job.get("content", "")),
                        "relevance_score": calculate_relevance_score(job, parsed_resume.get('Technical Skills', []))
                    }
                    all_jobs.append(enhanced_job)

            except Exception as e:
                print(f"Search query failed: {query} - {e}")
                continue

        # Remove duplicates and sort by relevance
        unique_jobs = remove_duplicate_jobs(all_jobs)
        sorted_jobs = sorted(unique_jobs, key=lambda x: x.get("relevance_score", 0), reverse=True)

        return sorted_jobs[:15]  # Return top 15 jobs

    except Exception as e:
        print(f"Job search error: {e}")
        # Return enhanced mock data on error
        return generate_india_focused_mock_jobs(parsed_resume.get('Technical Skills', []), parsed_resume.get('experience_level', 'Entry'))

def generate_india_focused_mock_jobs(tech_stack, experience_level):
    """Generate India-focused startup mock job data"""
    primary_skill = tech_stack[0] if tech_stack else "Software"

    # Indian cities
    indian_cities = ['Noida', 'Mumbai', 'Gurgaon', 'Bengaluru', 'Pune', 'Hyderabad', 'Lucknow', 'Chennai']

    # Indian startup job data
    indian_jobs = [
        {
            "title": f"{primary_skill} Engineer",
            "company": "Razorpay",
            "url": "https://razorpay.com/careers/",
            "apply_url": "https://razorpay.com/careers/",
            "location": "Bengaluru, Karnataka",
            "salary": "₹15,00,000 - ₹25,00,000",
            "source": "Razorpay Careers",
            "relevance_score": 95,
            "company_size": "1000-5000",
            "company_type": "Fintech Startup"
        },
        {
            "title": f"Senior {primary_skill} Developer",
            "company": "Swiggy",
            "url": "https://careers.swiggy.com/",
            "apply_url": "https://careers.swiggy.com/",
            "location": "Bengaluru, Karnataka",
            "salary": "₹18,00,000 - ₹30,00,000",
            "source": "Swiggy Careers",
            "relevance_score": 92,
            "company_size": "5000-10000",
            "company_type": "Food Tech Startup"
        },
        {
            "title": f"{experience_level.title()} Software Engineer",
            "company": "CRED",
            "url": "https://careers.cred.club/",
            "apply_url": "https://careers.cred.club/",
            "location": "Bengaluru, Karnataka",
            "salary": "₹20,00,000 - ₹35,00,000",
            "source": "CRED Careers",
            "relevance_score": 90,
            "company_size": "500-1000",
            "company_type": "Fintech Startup"
        },
        {
            "title": f"{primary_skill} Backend Engineer",
            "company": "Meesho",
            "url": "https://careers.meesho.com/",
            "apply_url": "https://careers.meesho.com/",
            "location": "Bengaluru, Karnataka",
            "salary": "₹16,00,000 - ₹28,00,000",
            "source": "Meesho Careers",
            "relevance_score": 88,
            "company_size": "1000-5000",
            "company_type": "E-commerce Startup"
        },
        {
            "title": f"Full Stack Developer - {primary_skill}",
            "company": "Unacademy",
            "url": "https://unacademy.com/careers",
            "apply_url": "https://unacademy.com/careers",
            "location": "Bengaluru, Karnataka",
            "salary": "₹14,00,000 - ₹24,00,000",
            "source": "Unacademy Careers",
            "relevance_score": 86,
            "company_size": "1000-5000",
            "company_type": "EdTech Startup"
        },
        {
            "title": f"{primary_skill} Engineer",
            "company": "PhonePe",
            "url": "https://www.phonepe.com/careers/",
            "apply_url": "https://www.phonepe.com/careers/",
            "location": "Bengaluru, Karnataka",
            "salary": "₹17,00,000 - ₹29,00,000",
            "source": "PhonePe Careers",
            "relevance_score": 94,
            "company_size": "5000-10000",
            "company_type": "Fintech Startup"
        },
        {
            "title": f"Software Engineer - {primary_skill}",
            "company": "Freshworks",
            "url": "https://www.freshworks.com/company/careers/",
            "apply_url": "https://www.freshworks.com/company/careers/",
            "location": "Chennai, Tamil Nadu",
            "salary": "₹12,00,000 - ₹22,00,000",
            "source": "Freshworks Careers",
            "relevance_score": 85,
            "company_size": "1000-5000",
            "company_type": "SaaS Startup"
        },
        {
            "title": f"{experience_level.title()} {primary_skill} Developer",
            "company": "Zomato",
            "url": "https://www.zomato.com/careers",
            "apply_url": "https://www.zomato.com/careers",
            "location": "Gurgaon, Haryana",
            "salary": "₹15,00,000 - ₹26,00,000",
            "source": "Zomato Careers",
            "relevance_score": 87,
            "company_size": "5000-10000",
            "company_type": "Food Tech Startup"
        }
    ]

    # Add some jobs in other Indian cities
    for i, city in enumerate(indian_cities[2:6]):  # Add jobs in Gurgaon, Pune, Hyderabad, Lucknow
        indian_jobs.append({
            "title": f"{primary_skill} Engineer",
            "company": f"TechStartup{i+1}",
            "url": f"https://techstartup{i+1}.com/careers",
            "apply_url": f"https://techstartup{i+1}.com/careers",
            "location": f"{city}, India",
            "salary": "₹10,00,000 - ₹18,00,000",
            "source": f"TechStartup{i+1} Careers",
            "relevance_score": 80 + i,
            "company_size": "50-200",
            "company_type": "Tech Startup"
        })

    return indian_jobs[:12]  # Return top 12 jobs

def generate_mock_jobs(tech_stack, experience_level):
    """Generate realistic mock job data with actual application links"""
    primary_skill = tech_stack[0] if tech_stack else "Software"

    # Real job data with actual application links
    real_jobs = [
        {
            "title": f"Senior {primary_skill} Engineer",
            "company": "Google",
            "url": "https://careers.google.com/jobs/results/?q=software%20engineer",
            "apply_url": "https://careers.google.com/jobs/results/?q=software%20engineer",
            "location": "Mountain View, CA",
            "salary": "$150,000 - $250,000",
            "source": "Google Careers",
            "relevance_score": 95
        },
        {
            "title": f"Full Stack Developer - {primary_skill}",
            "company": "Microsoft",
            "url": "https://careers.microsoft.com/us/en/search-results?keywords=software%20engineer",
            "apply_url": "https://careers.microsoft.com/us/en/search-results?keywords=software%20engineer",
            "location": "Seattle, WA",
            "salary": "$140,000 - $230,000",
            "source": "Microsoft Careers",
            "relevance_score": 92
        },
        {
            "title": f"{experience_level.title()} Software Engineer",
            "company": "Amazon",
            "url": "https://www.amazon.jobs/en/search?base_query=software%20engineer",
            "apply_url": "https://www.amazon.jobs/en/search?base_query=software%20engineer",
            "location": "Austin, TX",
            "salary": "$130,000 - $220,000",
            "source": "Amazon Jobs",
            "relevance_score": 90
        },
        {
            "title": f"{primary_skill} Backend Developer",
            "company": "Meta",
            "url": "https://www.metacareers.com/jobs/?q=software%20engineer",
            "apply_url": "https://www.metacareers.com/jobs/?q=software%20engineer",
            "location": "Menlo Park, CA",
            "salary": "$145,000 - $240,000",
            "source": "Meta Careers",
            "relevance_score": 88
        },
        {
            "title": f"Cloud Engineer - {primary_skill}",
            "company": "Netflix",
            "url": "https://jobs.netflix.com/search?q=software%20engineer",
            "apply_url": "https://jobs.netflix.com/search?q=software%20engineer",
            "location": "Los Gatos, CA",
            "salary": "$160,000 - $280,000",
            "source": "Netflix Jobs",
            "relevance_score": 86
        },
        {
            "title": f"{experience_level.title()} DevOps Engineer",
            "company": "Tesla",
            "url": "https://www.tesla.com/careers/search/?query=software%20engineer",
            "apply_url": "https://www.tesla.com/careers/search/?query=software%20engineer",
            "location": "Palo Alto, CA",
            "salary": "$120,000 - $200,000",
            "source": "Tesla Careers",
            "relevance_score": 84
        },
        {
            "title": f"Software Engineer - {primary_skill}",
            "company": "Uber",
            "url": "https://www.uber.com/careers/list/?query=software%20engineer",
            "apply_url": "https://www.uber.com/careers/list/?query=software%20engineer",
            "location": "San Francisco, CA",
            "salary": "$135,000 - $225,000",
            "source": "Uber Careers",
            "relevance_score": 82
        },
        {
            "title": f"Frontend Developer - {primary_skill}",
            "company": "Airbnb",
            "url": "https://careers.airbnb.com/positions/?search=software%20engineer",
            "apply_url": "https://careers.airbnb.com/positions/?search=software%20engineer",
            "location": "San Francisco, CA",
            "salary": "$140,000 - $230,000",
            "source": "Airbnb Careers",
            "relevance_score": 80
        },
        {
            "title": f"Data Engineer - {primary_skill}",
            "company": "Spotify",
            "url": "https://www.lifeatspotify.com/jobs?q=software%20engineer",
            "apply_url": "https://www.lifeatspotify.com/jobs?q=software%20engineer",
            "location": "New York, NY",
            "salary": "$125,000 - $210,000",
            "source": "Spotify Careers",
            "relevance_score": 78
        },
        {
            "title": f"{experience_level.title()} Product Engineer",
            "company": "Stripe",
            "url": "https://stripe.com/jobs/search?q=software%20engineer",
            "apply_url": "https://stripe.com/jobs/search?q=software%20engineer",
            "location": "San Francisco, CA",
            "salary": "$150,000 - $260,000",
            "source": "Stripe Careers",
            "relevance_score": 85
        },
        {
            "title": f"Software Engineer - Payments",
            "company": "PayPal",
            "url": "https://www.paypal.com/us/webapps/mpp/jobs/search?keywords=software%20engineer",
            "apply_url": "https://www.paypal.com/us/webapps/mpp/jobs/search?keywords=software%20engineer",
            "location": "San Jose, CA",
            "salary": "$130,000 - $220,000",
            "source": "PayPal Careers",
            "relevance_score": 83
        },
        {
            "title": f"Machine Learning Engineer",
            "company": "NVIDIA",
            "url": "https://www.nvidia.com/en-us/about-nvidia/careers/",
            "apply_url": "https://www.nvidia.com/en-us/about-nvidia/careers/",
            "location": "Santa Clara, CA",
            "salary": "$160,000 - $280,000",
            "source": "NVIDIA Careers",
            "relevance_score": 87
        }
    ]

    # Add job descriptions
    for job in real_jobs:
        job["content"] = f"Exciting {experience_level}-level opportunity at {job['company']} working with {primary_skill} and modern technologies. Competitive salary: {job['salary']}. Location: {job['location']}. Apply now!"

    return real_jobs

def extract_apply_url(job_url, content):
    """Extract or generate apply URL from job data"""
    if not job_url:
        return ""

    # If it's already a direct application URL, return it
    if any(keyword in job_url.lower() for keyword in ['apply', 'application', 'careers', 'jobs']):
        return job_url

    # Try to extract apply URL from content
    import re
    apply_patterns = [
        r'https?://[^\s]+(?:apply|application|careers)[^\s]*',
        r'Apply\s+(?:at|here):\s*(https?://[^\s]+)',
        r'Application\s+link:\s*(https?://[^\s]+)'
    ]

    for pattern in apply_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            return match.group(1) if match.groups() else match.group(0)

    # Generate apply URL based on job source
    if "linkedin.com" in job_url:
        return job_url  # LinkedIn URLs are usually direct apply links
    elif "indeed.com" in job_url:
        return job_url  # Indeed URLs are usually direct apply links
    elif "glassdoor.com" in job_url:
        return job_url  # Glassdoor URLs are usually direct apply links
    else:
        # For company websites, try to construct careers page URL
        domain = job_url.split("//")[1].split("/")[0] if "//" in job_url else job_url.split("/")[0]
        return f"https://{domain}/careers" if domain else job_url

def extract_location_from_content(content):
    """Extract location information from job content"""
    if not content:
        return "Location not specified"

    import re
    # Common location patterns
    location_patterns = [
        r'(?:Location|Based in|Office in):\s*([^,\n]+(?:,\s*[A-Z]{2})?)',
        r'([A-Za-z\s]+,\s*[A-Z]{2}(?:\s+\d{5})?)',  # City, State format
        r'(Remote|Work from home|Hybrid)',
        r'([A-Za-z\s]+,\s*[A-Za-z\s]+)(?:\s*-\s*Remote)?'  # City, Country format
    ]

    for pattern in location_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            return match.group(1).strip()

    # Check for remote work indicators
    if any(keyword in content.lower() for keyword in ['remote', 'work from home', 'distributed']):
        return "Remote"

    return "Location not specified"

def extract_salary_from_content(content):
    """Extract salary information from job content"""
    if not content:
        return "Salary not specified"

    import re
    # Common salary patterns
    salary_patterns = [
        r'\$[\d,]+(?:\s*-\s*\$[\d,]+)?(?:\s*(?:per year|annually|/year))?',
        r'[\d,]+k?\s*-\s*[\d,]+k?(?:\s*(?:per year|annually|/year))?',
        r'Salary:\s*([^\n]+)',
        r'Compensation:\s*([^\n]+)',
        r'Pay:\s*([^\n]+)'
    ]

    for pattern in salary_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            return match.group(0).strip()

    return "Salary not specified"

def extract_company_from_url(url):
    """Extract company name from job URL"""
    if not url:
        return "Unknown Company"

    # Common patterns for extracting company names
    if "careers." in url:
        domain = url.split("careers.")[1].split("/")[0]
        return domain.split(".")[0].title()
    elif "jobs." in url:
        domain = url.split("jobs.")[1].split("/")[0]
        return domain.split(".")[0].title()
    else:
        domain = url.split("//")[1].split("/")[0] if "//" in url else url.split("/")[0]
        return domain.split(".")[0].title()

def extract_source_from_url(url):
    """Extract job board/source from URL"""
    if not url:
        return "Direct"

    if "linkedin.com" in url:
        return "LinkedIn"
    elif "indeed.com" in url:
        return "Indeed"
    elif "glassdoor.com" in url:
        return "Glassdoor"
    elif "dice.com" in url:
        return "Dice"
    elif "remote.co" in url:
        return "Remote.co"
    elif "wellfound.com" in url:
        return "Wellfound"
    else:
        return "Company Website"

def calculate_relevance_score(job, tech_stack):
    """Calculate relevance score based on job content and user skills"""
    score = 50  # Base score

    title = job.get("title", "").lower()
    content = job.get("content", "").lower()

    # Boost score for matching skills
    for skill in tech_stack:
        if skill.lower() in title:
            score += 15
        elif skill.lower() in content:
            score += 10

    # Boost for senior positions if user has experience
    if "senior" in title or "lead" in title:
        score += 10

    # Boost for remote opportunities
    if "remote" in title or "remote" in content:
        score += 5

    return min(score, 100)  # Cap at 100

def remove_duplicate_jobs(jobs):
    """Remove duplicate job postings based on title and company"""
    seen = set()
    unique_jobs = []

    for job in jobs:
        key = (job.get("title", "").lower(), job.get("company", "").lower())
        if key not in seen:
            seen.add(key)
            unique_jobs.append(job)

    return unique_jobs

job_search_tool = Tool(
    name="JobFinder",
    func=search_jobs,
    description="Search jobs matching the user's tech stack"
)

def job_node(state):
    if "email" not in state["history"]:
        state["history"]["email"] = "<EMAIL>"  # fallback if needed
    jobs = search_jobs(state["parsed"])
    state["jobs"] = jobs
    return state