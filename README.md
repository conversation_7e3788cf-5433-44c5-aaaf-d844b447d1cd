# 🤖 AI Resume Intelligence Assistant

An intelligent resume analysis and job recommendation system that uses AI to parse resumes and find real job opportunities in India.

## ✨ Features

- **🔍 Smart Resume Parsing**: LLM-powered resume analysis with proper content grouping
- **💼 Real Job Search**: Web search integration to find actual job openings (no fake data)
- **🇮🇳 India-Focused**: Specialized for Indian job market with city-specific searches
- **🚀 Startup Focus**: Prioritizes startup and tech companies
- **🔗 Working Apply Links**: Direct links to LinkedIn, Naukri, and company career pages
- **📊 Interactive UI**: Clean Streamlit interface for easy use

## 🛠️ Tech Stack

- **Backend**: FastAPI, Python
- **Frontend**: Streamlit
- **AI/ML**: Google Gemini, LangChain
- **Web Search**: Tavily API
- **Data Processing**: PyPDF2, python-docx

## 🚀 Quick Start

### 1. Clone Repository
```bash
git clone <your-repo-url>
cd resume-intelligence-assistant
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Set Environment Variables
Create a `.env` file:
```env
GOOGLE_API_KEY=your_gemini_api_key
TAVILY_API_KEY=your_tavily_api_key
```

### 4. Run the Application
```bash
# Start FastAPI backend
uvicorn main:app --host 0.0.0.0 --port 8001 --reload

# Start Streamlit frontend (in another terminal)
streamlit run app.py --server.port 8002
```

### 5. Access the Application
- **Frontend**: http://localhost:8002
- **Backend API**: http://localhost:8001

## 📋 How to Use

1. **Upload Resume**: Upload your PDF/DOCX resume
2. **AI Analysis**: System parses and extracts key information
3. **Find Jobs**: Click "Find Job Recommendations" 
4. **Apply**: Click on real job links to apply directly

## 🎯 Key Improvements

### Real Job Search
- ✅ No more fake/mock job data
- ✅ Real web search using Tavily
- ✅ Working apply links (no 404 errors)
- ✅ India-specific job portals

### Enhanced Resume Parsing
- ✅ LLM-based parsing with context
- ✅ Proper content grouping
- ✅ Structured data extraction
- ✅ India job market optimization

### Startup Focus
- ✅ Targets Indian startups and tech companies
- ✅ City-specific searches (Noida, Mumbai, Gurgaon, Bengaluru, etc.)
- ✅ Salary ranges in INR
- ✅ Experience level matching

## 📁 Project Structure

```
├── agents/
│   ├── job.py          # Real job search agent
│   ├── manager.py      # Workflow manager
│   └── parser.py       # Resume parsing agent
├── prompts/
│   ├── job_prompt.py   # Job search strategy
│   └── parser_prompt.py # Resume parsing instructions
├── utils/
│   ├── data_validator.py # Data validation
│   └── file_parser.py   # File processing
├── app.py              # Streamlit frontend
├── main.py             # FastAPI backend
└── requirements.txt    # Dependencies
```

## 🔧 Configuration

### API Keys Required
- **Google Gemini**: For LLM-based resume parsing
- **Tavily**: For real job web search

### Supported File Formats
- PDF (.pdf)
- Word Documents (.docx)

## 🌟 Features in Detail

### Real Job Discovery
- LinkedIn job searches for Indian cities
- Naukri.com integration
- Company career page searches
- Startup job portals (Instahyre, CutShort, AngelList)

### Smart Resume Analysis
- Skill extraction and matching
- Experience level detection
- Job title recommendations
- India market optimization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions, please create an issue in the GitHub repository.

---

**Made with ❤️ for the Indian job market**
