#!/usr/bin/env python3
"""
Test the updated startup-focused job search prompt
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.job import search_jobs
import json

def test_startup_job_search():
    """Test the startup-focused job search with sample resume data"""
    print("🧪 Testing Startup-Focused Job Search...")
    print("=" * 60)
    
    # Sample parsed resume data (similar to <PERSON><PERSON><PERSON>'s profile)
    sample_resume = {
        "name": "<PERSON>ratham Dixit",
        "email": "<EMAIL>",
        "phone": "+91-6307803699",
        "location": "Lucknow, Uttar Pradesh",
        "total_years_experience": 0.25,
        "experience_level": "Entry",
        "primary_skill_domain": "AI/ML",
        "target_job_titles": ["ML Engineer", "AI Engineer", "Software Engineer"],
        "Technical Skills": [
            "Python", "Machine Learning", "Deep Learning", "NLP", 
            "Computer Vision", "TensorFlow", "PyTorch", "Langchain",
            "FastAPI", "Flask", "SQL", "Git"
        ],
        "technical_skills": {
            "programming_languages": ["Python", "C++", "SQL"],
            "ai_ml": ["Machine Learning", "Deep Learning", "NLP", "Computer Vision", "TensorFlow", "PyTorch"],
            "frameworks": ["Langchain", "FastAPI", "Flask"],
            "databases": ["Vector Databases", "FAISS"],
            "other_tools": ["Git", "VS Code", "Jupyter Notebook"]
        },
        "work_experience": [
            {
                "company": "NGSPUR Pvt. Ltd.",
                "role": "ML Intern",
                "duration": "March 2025 - June 2025",
                "description": "Developed computer vision applications using YOLO",
                "technologies_used": ["YOLO", "Computer Vision", "Flask"]
            }
        ],
        "projects": [
            {
                "title": "Multiformat Document Retrieval and QA App",
                "tech": ["Langchain", "FastAPI", "FAISS", "Google Generative AI"],
                "description": "RAG application for document Q&A"
            },
            {
                "title": "Agribot-AI Chatbot",
                "tech": ["Python", "Langchain", "Gemini API"],
                "description": "Multilingual chatbot for farmers"
            }
        ]
    }
    
    try:
        print("📋 Sample Resume Profile:")
        print(f"   Name: {sample_resume['name']}")
        print(f"   Experience Level: {sample_resume['experience_level']}")
        print(f"   Primary Domain: {sample_resume['primary_skill_domain']}")
        print(f"   Location: {sample_resume['location']}")
        print(f"   Key Skills: {sample_resume['Technical Skills'][:5]}")
        
        print("\n🔍 Running Startup-Focused Job Search...")
        
        # Run job search
        jobs = search_jobs(sample_resume)
        
        print(f"\n📊 Job Search Results:")
        print(f"   Total jobs found: {len(jobs)}")
        
        if jobs:
            # Analyze results for startup focus and India locations
            startup_jobs = 0
            india_jobs = 0
            skill_matches = 0
            
            print(f"\n📋 Job Analysis:")
            
            for i, job in enumerate(jobs[:5]):  # Analyze first 5 jobs
                print(f"\n   Job {i+1}:")
                print(f"     Title: {job.get('title', 'N/A')}")
                print(f"     Company: {job.get('company', 'N/A')}")
                print(f"     Location: {job.get('location', 'N/A')}")
                print(f"     Source: {job.get('source', 'N/A')}")
                print(f"     Apply URL: {job.get('apply_url', 'N/A')[:60]}...")
                
                # Check for India locations
                location = job.get('location', '').lower()
                india_keywords = ['noida', 'mumbai', 'gurgaon', 'bengaluru', 'bangalore', 'pune', 'hyderabad', 'lucknow', 'indore', 'ahmedabad', 'chennai', 'kolkata', 'india', 'remote - india']
                is_india_job = any(keyword in location for keyword in india_keywords)
                
                # Check for startup indicators
                company = job.get('company', '').lower()
                startup_keywords = ['startup', 'pvt ltd', 'technologies', 'labs', 'solutions']
                is_startup = any(keyword in company for keyword in startup_keywords) or job.get('company_size', 0) < 500
                
                # Check skill matching
                job_skills = job.get('skills_required', [])
                user_skills = [skill.lower() for skill in sample_resume['Technical Skills']]
                matching_skills = [skill for skill in job_skills if any(user_skill in skill.lower() for user_skill in user_skills)]
                skill_match_score = len(matching_skills) / max(len(job_skills), 1) * 100 if job_skills else 0
                
                if is_india_job:
                    india_jobs += 1
                if is_startup:
                    startup_jobs += 1
                if skill_match_score > 50:
                    skill_matches += 1
                
                print(f"     India Job: {'✅' if is_india_job else '❌'}")
                print(f"     Startup/Small Company: {'✅' if is_startup else '❌'}")
                print(f"     Skill Match: {skill_match_score:.1f}%")
            
            print(f"\n📈 Search Quality Analysis:")
            total_analyzed = min(len(jobs), 5)
            print(f"   India-focused jobs: {india_jobs}/{total_analyzed} ({india_jobs/total_analyzed*100:.1f}%)")
            print(f"   Startup/Small company jobs: {startup_jobs}/{total_analyzed} ({startup_jobs/total_analyzed*100:.1f}%)")
            print(f"   Good skill matches (>50%): {skill_matches}/{total_analyzed} ({skill_matches/total_analyzed*100:.1f}%)")
            
            # Success criteria
            success_score = (india_jobs + startup_jobs + skill_matches) / (total_analyzed * 3) * 100
            print(f"   Overall Success Score: {success_score:.1f}%")
            
            if success_score > 60:
                print("   ✅ Job search quality: GOOD")
            elif success_score > 40:
                print("   ⚠️ Job search quality: MODERATE")
            else:
                print("   ❌ Job search quality: NEEDS IMPROVEMENT")
                
        else:
            print("   ❌ No jobs found")
            
        return jobs
        
    except Exception as e:
        print(f"❌ Error during job search: {e}")
        return None

def main():
    """Main test function"""
    print("🚀 Testing Updated Startup Job Search Prompt...")
    print("=" * 50)
    
    jobs = test_startup_job_search()
    
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    
    if jobs:
        print(f"   ✅ Job Search: Found {len(jobs)} jobs")
        print("   🎯 Focus Areas Tested:")
        print("     - India location filtering")
        print("     - Startup/small company focus") 
        print("     - Skill matching accuracy")
        print("     - Direct application links")
    else:
        print("   ❌ Job Search: Failed to find jobs")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    main()
