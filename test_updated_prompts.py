#!/usr/bin/env python3
"""
Test script to verify the updated parser and job search prompts work correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.parser import parser_node
from agents.job import job_node
from agents.manager import manager_agent
from utils.file_parser import extract_text_from_file
import json

def test_parser_with_pratham_resume():
    """Test the updated parser prompt with <PERSON><PERSON><PERSON>'s resume"""
    print("🧪 Testing Updated Parser Prompt...")
    print("=" * 50)
    
    # Read <PERSON><PERSON><PERSON>'s resume
    try:
        with open("Pratham_resume_AI.pdf", "rb") as f:
            file_bytes = f.read()
        
        # Extract text
        resume_text = extract_text_from_file(file_bytes, "Pratham_resume_AI.pdf")
        print(f"📄 Resume text extracted: {len(resume_text)} characters")
        
        # Parse with updated prompt using manager agent
        result = manager_agent.process_resume_upload(
            file_name="Pratham_resume_AI.pdf",
            file_content=file_bytes,
            user_name="<PERSON><PERSON><PERSON> Dixit",
            email="<EMAIL>"
        )

        parsed_data = result.get("parsed", {})
        
        print("\n📋 Parsed Data Structure:")
        print(f"   Name: {parsed_data.get('name', 'NOT FOUND')}")
        print(f"   Email: {parsed_data.get('email', 'NOT FOUND')}")
        print(f"   Phone: {parsed_data.get('phone', 'NOT FOUND')}")
        
        # Check projects parsing (should be grouped, not line-by-line)
        projects = parsed_data.get('projects', [])
        print(f"\n📊 Projects Parsing Check:")
        print(f"   Number of projects: {len(projects)}")
        
        if projects:
            for i, project in enumerate(projects[:2]):  # Show first 2 projects
                print(f"   Project {i+1}:")
                print(f"     Title: {project.get('title', 'N/A')}")
                print(f"     Description length: {len(project.get('description', ''))}")
                print(f"     Technologies: {project.get('tech', [])}")
        
        # Check work experience parsing
        work_exp = parsed_data.get('work_experience', [])
        print(f"\n💼 Work Experience Parsing Check:")
        print(f"   Number of work experiences: {len(work_exp)}")
        
        if work_exp:
            for i, exp in enumerate(work_exp):
                print(f"   Experience {i+1}:")
                print(f"     Company: {exp.get('company', 'N/A')}")
                print(f"     Role: {exp.get('role', 'N/A')}")
                print(f"     Description length: {len(exp.get('description', ''))}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error testing parser: {e}")
        return None

def test_job_search_with_india_focus(result):
    """Test the updated job search prompt with India focus"""
    print("\n🧪 Testing Updated Job Search Prompt (India Focus)...")
    print("=" * 60)

    if not result:
        print("❌ No result data available for job search test")
        return

    try:
        # Test job search with the parsed data
        jobs = result.get("jobs", [])
        if not jobs:
            print("   No jobs found in result data, this might be expected for this test")
        print(f"📊 Job Search Results:")
        print(f"   Total jobs found: {len(jobs)}")
        
        # Check if jobs are India-focused
        india_jobs = 0
        linkedin_jobs = 0
        
        for i, job in enumerate(jobs[:5]):  # Check first 5 jobs
            location = job.get('location', '').lower()
            source = job.get('source', '').lower()
            
            # Check for India locations
            india_keywords = ['india', 'bangalore', 'mumbai', 'delhi', 'hyderabad', 'chennai', 'pune', 'gurgaon', 'noida', 'remote - india']
            is_india_job = any(keyword in location for keyword in india_keywords)
            
            if is_india_job:
                india_jobs += 1
            
            if 'linkedin' in source:
                linkedin_jobs += 1
            
            print(f"\n   Job {i+1}:")
            print(f"     Title: {job.get('title', 'N/A')}")
            print(f"     Company: {job.get('company', 'N/A')}")
            print(f"     Location: {job.get('location', 'N/A')}")
            print(f"     Source: {job.get('source', 'N/A')}")
            print(f"     India Job: {'✅' if is_india_job else '❌'}")
            print(f"     Apply URL: {job.get('apply_url', 'N/A')[:50]}...")
        
        print(f"\n📈 India Focus Analysis:")
        print(f"   India-based jobs: {india_jobs}/{min(len(jobs), 5)}")
        print(f"   LinkedIn jobs: {linkedin_jobs}/{min(len(jobs), 5)}")
        print(f"   India focus success rate: {(india_jobs/min(len(jobs), 5)*100):.1f}%" if jobs else "0%")
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error testing job search: {e}")
        return None

def main():
    """Main test function"""
    print("🚀 Testing Updated Prompts...")
    print("=" * 40)
    
    # Test parser
    result = test_parser_with_pratham_resume()

    # Test job search
    jobs = test_job_search_with_india_focus(result)

    parsed_data = result.get("parsed", {}) if result else {}
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    
    if parsed_data:
        projects = parsed_data.get('projects', [])
        work_exp = parsed_data.get('work_experience', [])
        
        print(f"   ✅ Parser: Successfully parsed resume")
        print(f"   📋 Projects: {len(projects)} (should be grouped, not line-by-line)")
        print(f"   💼 Work Experience: {len(work_exp)} (should be grouped)")
    else:
        print(f"   ❌ Parser: Failed to parse resume")
    
    if jobs:
        india_jobs = sum(1 for job in jobs[:5] if any(keyword in job.get('location', '').lower() 
                        for keyword in ['india', 'bangalore', 'mumbai', 'delhi', 'hyderabad', 'chennai', 'pune']))
        linkedin_jobs = sum(1 for job in jobs[:5] if 'linkedin' in job.get('source', '').lower())
        
        print(f"   ✅ Job Search: Found {len(jobs)} jobs")
        print(f"   🇮🇳 India Focus: {india_jobs}/{min(len(jobs), 5)} jobs")
        print(f"   💼 LinkedIn Priority: {linkedin_jobs}/{min(len(jobs), 5)} jobs")
    else:
        print(f"   ❌ Job Search: Failed to find jobs")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    main()
