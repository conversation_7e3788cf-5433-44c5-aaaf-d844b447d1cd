#!/usr/bin/env python3
"""
End-to-end test to verify the complete resume processing pipeline
"""

import sys
import os
import json
import requests
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_endpoint():
    """Test the API endpoint directly"""
    print("🧪 Testing API Endpoint...")
    print("=" * 50)
    
    # Create a sample resume file content
    sample_resume = """
    <PERSON>
    Senior Software Engineer
    <EMAIL>
    +1-555-123-4567
    linkedin.com/in/johnsmith
    github.com/johnsmith
    
    TECHNICAL SKILLS:
    Python, JavaScript, React, Node.js, Django, PostgreSQL, AWS, Docker, Kubernetes
    
    EXPERIENCE:
    Senior Software Engineer at TechCorp (2021-Present)
    - Led development of microservices architecture using Python and Django
    - Built responsive web applications with React and Node.js
    - Deployed applications on AWS using Docker and Kubernetes
    
    Software Engineer at StartupXYZ (2019-2021)
    - Developed REST APIs using Python and Django
    - Worked with PostgreSQL databases
    
    EDUCATION:
    Master of Science in Computer Science
    University of Technology (2017-2019)
    
    Bachelor of Science in Computer Science
    Tech University (2015-2017)
    """
    
    try:
        # Test API availability
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API server is running")
        else:
            print("❌ API server health check failed")
            return False
    except Exception as e:
        print(f"❌ API server not accessible: {e}")
        return False
    
    try:
        # Create a file-like object for the API (use .docx extension to pass validation)
        files = {
            'file': ('john_smith_resume.docx', sample_resume.encode('utf-8'), 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        }
        
        # Test the upload endpoint
        response = requests.post(
            "http://localhost:8000/upload_resume_simple",
            files=files,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Resume upload successful")
            print(f"   Session ID: {result.get('session_id', 'Missing')}")
            print(f"   Message: {result.get('message', 'Missing')}")
            
            # Check parsed data
            parsed_data = result.get('parsed', {})
            if parsed_data:
                print("✅ Resume data parsed successfully")
                print(f"   Name: {parsed_data.get('name', 'Missing')}")
                print(f"   Email: {parsed_data.get('email', 'Missing')}")
                print(f"   Skills: {parsed_data.get('Technical Skills', [])}")
                print(f"   Experience: {len(parsed_data.get('experience', []))} entries")
                print(f"   Education: {len(parsed_data.get('education', []))} entries")
                
                # Check if jobs were included
                jobs = result.get('jobs', [])
                if jobs:
                    print(f"✅ Job recommendations included: {len(jobs)} jobs")
                    
                    # Check first job for apply links
                    first_job = jobs[0]
                    if first_job.get('apply_url'):
                        print(f"✅ Apply links included: {first_job['apply_url']}")
                    else:
                        print("❌ Apply links missing")
                        
                else:
                    print("⚠️ No job recommendations included")
                
                return True
            else:
                print("❌ No parsed data returned")
                return False
        else:
            print(f"❌ Resume upload failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def test_json_storage():
    """Test JSON storage and retrieval"""
    print("\n🧪 Testing JSON Storage...")
    print("=" * 50)
    
    from utils.db import session_manager
    
    # Test session creation and data storage
    try:
        session_id = session_manager.create_session(
            user_name="Test User",
            email="<EMAIL>",
            resume_data={
                "filename": "test_resume.txt",
                "content": b"test content"
            }
        )
        
        print(f"✅ Session created: {session_id}")
        
        # Test session update with parsed data
        parsed_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "Technical Skills": ["Python", "JavaScript", "React"],
            "experience": ["Software Engineer at TechCorp"],
            "education": ["BS Computer Science"]
        }
        
        session_manager.update_session(session_id, {
            "resume_data": {
                "filename": "test_resume.txt",
                "parsed": parsed_data
            },
            "job_recommendations": [
                {
                    "title": "Software Engineer",
                    "company": "Google",
                    "apply_url": "https://careers.google.com/jobs/results/?q=software%20engineer"
                }
            ]
        })
        
        print("✅ Session updated with parsed data")
        
        # Test session retrieval
        session = session_manager.get_session(session_id)
        if session:
            print("✅ Session retrieved successfully")
            
            # Check if data is properly stored as JSON
            stored_parsed = session.get("resume_data", {}).get("parsed", {})
            stored_jobs = session.get("job_recommendations", [])
            
            if stored_parsed and stored_jobs:
                print("✅ Resume data and jobs stored correctly")
                print(f"   Stored name: {stored_parsed.get('name')}")
                print(f"   Stored skills: {stored_parsed.get('Technical Skills')}")
                print(f"   Stored jobs: {len(stored_jobs)}")
                
                # Test JSON serialization
                json_str = json.dumps(stored_parsed)
                json_back = json.loads(json_str)
                
                if json_back == stored_parsed:
                    print("✅ JSON serialization works correctly")
                    return True
                else:
                    print("❌ JSON serialization failed")
                    return False
            else:
                print("❌ Data not stored correctly")
                return False
        else:
            print("❌ Session retrieval failed")
            return False
            
    except Exception as e:
        print(f"❌ JSON storage test failed: {e}")
        return False

def test_job_apply_links():
    """Test that job recommendations include apply links"""
    print("\n🧪 Testing Job Apply Links...")
    print("=" * 50)
    
    from agents.job import search_jobs
    
    # Test with sample resume data
    sample_resume = {
        "Technical Skills": ["Python", "JavaScript", "React", "Django"],
        "experience": [
            {"title": "Software Engineer", "company": "TechCorp"}
        ],
        "education": [
            {"degree": "Computer Science", "school": "University"}
        ]
    }
    
    try:
        jobs = search_jobs(sample_resume)
        
        if jobs:
            print(f"✅ Found {len(jobs)} job recommendations")
            
            # Check that all jobs have apply links
            jobs_with_apply_links = 0
            for job in jobs:
                if job.get('apply_url'):
                    jobs_with_apply_links += 1
            
            if jobs_with_apply_links == len(jobs):
                print("✅ All jobs include apply links")
                
                # Show sample job
                sample_job = jobs[0]
                print(f"   Sample job: {sample_job['title']} at {sample_job['company']}")
                print(f"   Apply URL: {sample_job['apply_url']}")
                print(f"   Location: {sample_job.get('location', 'Not specified')}")
                print(f"   Salary: {sample_job.get('salary', 'Not specified')}")
                
                return True
            else:
                print(f"❌ Only {jobs_with_apply_links}/{len(jobs)} jobs have apply links")
                return False
        else:
            print("❌ No job recommendations found")
            return False
            
    except Exception as e:
        print(f"❌ Job apply links test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting End-to-End Tests...")
    print("=" * 60)
    
    # Run all tests
    test1_result = test_api_endpoint()
    test2_result = test_json_storage()
    test3_result = test_job_apply_links()
    
    print("\n" + "=" * 60)
    print("📊 End-to-End Test Summary:")
    print(f"   API Endpoint: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   JSON Storage: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"   Job Apply Links: {'✅ PASS' if test3_result else '❌ FAIL'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 All end-to-end tests passed!")
        print("✅ Resume data is parsed and stored successfully in JSON form")
        print("✅ Job agent provides apply links with all relevant roles")
        print("✅ The system is working correctly!")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")
    
    print("\n🌐 Your application is ready!")
    print("   Streamlit App: http://localhost:8504")
    print("   API Server: http://localhost:8000")
