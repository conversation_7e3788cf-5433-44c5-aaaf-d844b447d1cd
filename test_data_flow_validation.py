#!/usr/bin/env python3
"""
Comprehensive test to verify data flows correctly with validation throughout the system
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_validation():
    """Test the data validation system"""
    print("🧪 Testing Data Validation System...")
    print("=" * 50)
    
    from utils.data_validator import ResumeDataValidator, validate_and_clean_data
    
    # Test 1: Valid resume data
    valid_resume = {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "Technical Skills": ["Python", "JavaScript", "React"],
        "experience": ["Software Engineer at TechCorp"],
        "education": ["BS Computer Science"]
    }
    
    is_valid, issues, cleaned = ResumeDataValidator.validate_parsed_resume(valid_resume)
    print(f"✅ Valid resume test: {is_valid}, Quality Score: {cleaned.get('data_quality_score', 0)}")
    
    # Test 2: Invalid resume data
    invalid_resume = {
        "name": "",  # Missing name
        "Technical Skills": [],  # Empty skills
        "random_field": "value"
    }
    
    is_valid, issues, cleaned = ResumeDataValidator.validate_parsed_resume(invalid_resume)
    print(f"❌ Invalid resume test: {is_valid}, Issues: {len(issues)}")
    
    # Test 3: Job data validation
    valid_jobs = [
        {
            "title": "Software Engineer",
            "company": "Google",
            "apply_url": "https://careers.google.com/jobs/123",
            "location": "Mountain View, CA",
            "salary": "$150,000"
        }
    ]
    
    jobs_valid, job_issues, cleaned_jobs = ResumeDataValidator.validate_job_data(valid_jobs)
    print(f"✅ Valid jobs test: {jobs_valid}, Jobs: {len(cleaned_jobs)}")
    
    return True

def test_manager_agent_with_validation():
    """Test manager agent with validation"""
    print("\n🧪 Testing Manager Agent with Validation...")
    print("=" * 50)
    
    from agents.manager import manager_agent
    
    # Create comprehensive resume content
    resume_content = """
Michael Chen
Senior Software Engineer
<EMAIL>
+1-555-123-4567
linkedin.com/in/michaelchen
github.com/michaelchen

TECHNICAL SKILLS:
Python, JavaScript, TypeScript, React, Vue.js, Node.js, Django, FastAPI, Flask,
PostgreSQL, MongoDB, Redis, AWS, Azure, Docker, Kubernetes, Git, Jenkins, CI/CD

PROFESSIONAL EXPERIENCE:

Senior Software Engineer | TechCorp Inc. | 2020 - Present
• Led development of microservices architecture using Python and Django
• Built responsive web applications with React and TypeScript
• Deployed applications on AWS using Docker and Kubernetes
• Mentored 5 junior developers and conducted code reviews
• Improved system performance by 40% through optimization

Software Engineer | StartupXYZ | 2018 - 2020
• Developed REST APIs using Node.js and Express
• Created dynamic user interfaces with Vue.js
• Worked with PostgreSQL and MongoDB databases
• Implemented CI/CD pipelines using Jenkins

Junior Developer | WebCorp | 2016 - 2018
• Built web applications using JavaScript and React
• Collaborated with design team on UI/UX improvements
• Participated in agile development processes

EDUCATION:
Master of Science in Computer Science | Stanford University | 2014-2016
Bachelor of Science in Software Engineering | UC Berkeley | 2010-2014

PROJECTS:
• E-commerce Platform: Full-stack application using Django, React, and PostgreSQL
• Real-time Chat App: Built with Node.js, Socket.io, and Redis
• Data Analytics Dashboard: Created with Python, React, and D3.js

CERTIFICATIONS:
• AWS Certified Solutions Architect - Associate
• Google Cloud Professional Developer
• Certified Kubernetes Administrator (CKA)
"""
    
    try:
        result = manager_agent.process_resume_upload(
            file_name="michael_chen_resume.txt",
            file_content=resume_content.encode('utf-8'),
            user_name="Michael Chen",
            email="<EMAIL>"
        )
        
        print("📋 Manager Agent Results:")
        print(f"   Success: {'error' not in result}")
        print(f"   Session ID: {result.get('session_id', 'Missing')}")
        print(f"   Validation Status: {result.get('validation_status', 'Missing')}")
        print(f"   Data Quality Score: {result.get('data_quality_score', 'Missing')}")
        
        # Check parsed data
        parsed_data = result.get("parsed", {})
        if parsed_data:
            print(f"\n📄 Parsed Data Validation:")
            print(f"   Name: {parsed_data.get('name', 'Missing')}")
            print(f"   Email: {parsed_data.get('email', 'Missing')}")
            print(f"   Skills Count: {len(parsed_data.get('Technical Skills', []))}")
            print(f"   Experience Count: {len(parsed_data.get('experience', []))}")
            print(f"   Education Count: {len(parsed_data.get('education', []))}")
            print(f"   Quality Score: {parsed_data.get('data_quality_score', 'Missing')}")
            print(f"   Is Fallback: {parsed_data.get('is_fallback', False)}")
        
        # Check jobs data
        jobs = result.get("jobs", [])
        if jobs:
            print(f"\n💼 Jobs Data Validation:")
            print(f"   Jobs Count: {len(jobs)}")
            
            # Check apply links
            jobs_with_apply = sum(1 for job in jobs if job.get('apply_url'))
            print(f"   Jobs with Apply Links: {jobs_with_apply}/{len(jobs)}")
            
            # Check required fields
            valid_jobs = 0
            for job in jobs:
                if all(job.get(field) for field in ['title', 'company', 'apply_url']):
                    valid_jobs += 1
            
            print(f"   Valid Jobs: {valid_jobs}/{len(jobs)}")
            
            # Sample job
            if jobs:
                sample_job = jobs[0]
                print(f"   Sample Job: {sample_job.get('title', 'Unknown')} at {sample_job.get('company', 'Unknown')}")
                print(f"   Sample Apply URL: {sample_job.get('apply_url', 'Missing')}")
        
        # Test JSON serialization
        try:
            json_str = json.dumps(result)
            json_back = json.loads(json_str)
            print(f"\n💾 JSON Serialization: ✅ Success")
            
            if json_back == result:
                print(f"   Data Integrity: ✅ Preserved")
                return True
            else:
                print(f"   Data Integrity: ❌ Lost")
                return False
                
        except Exception as e:
            print(f"   JSON Serialization: ❌ Failed - {e}")
            return False
            
    except Exception as e:
        print(f"❌ Manager agent test failed: {e}")
        return False

def test_session_data_flow():
    """Test complete session data flow"""
    print("\n🧪 Testing Session Data Flow...")
    print("=" * 50)
    
    from utils.db import session_manager
    from utils.data_validator import validate_and_clean_data
    
    try:
        # Create session
        session_id = session_manager.create_session(
            user_name="Test User",
            email="<EMAIL>",
            resume_data={"filename": "test.txt", "content": b"test"}
        )
        
        print(f"✅ Session created: {session_id}")
        
        # Test data storage and retrieval
        test_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "Technical Skills": ["Python", "JavaScript"],
            "experience": ["Software Engineer"],
            "education": ["Computer Science"]
        }
        
        # Validate before storing
        validated_data = validate_and_clean_data(test_data, "resume")
        
        # Update session
        session_manager.update_session(session_id, {
            "resume_data": {
                "filename": "test.txt",
                "parsed": validated_data
            },
            "job_recommendations": [
                {
                    "title": "Software Engineer",
                    "company": "Google",
                    "apply_url": "https://careers.google.com/jobs/123"
                }
            ]
        })
        
        print(f"✅ Session updated with validated data")
        
        # Retrieve and validate
        session = session_manager.get_session(session_id)
        if session:
            stored_data = session.get("resume_data", {}).get("parsed", {})
            stored_jobs = session.get("job_recommendations", [])
            
            print(f"✅ Session retrieved successfully")
            print(f"   Stored data quality: {stored_data.get('data_quality_score', 'Missing')}")
            print(f"   Stored jobs count: {len(stored_jobs)}")
            
            # Validate retrieved data
            validated_retrieved = validate_and_clean_data(stored_data, "resume")
            validated_jobs = validate_and_clean_data(stored_jobs, "jobs")
            
            if validated_retrieved and validated_jobs:
                print(f"✅ Retrieved data validation passed")
                return True
            else:
                print(f"❌ Retrieved data validation failed")
                return False
        else:
            print(f"❌ Session retrieval failed")
            return False
            
    except Exception as e:
        print(f"❌ Session data flow test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Complete Data Flow with Validation...")
    print("=" * 60)
    
    test1_result = test_data_validation()
    test2_result = test_manager_agent_with_validation()
    test3_result = test_session_data_flow()
    
    print("\n" + "=" * 60)
    print("📊 Comprehensive Test Results:")
    print(f"   Data Validation: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Manager Agent: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"   Session Data Flow: {'✅ PASS' if test3_result else '❌ FAIL'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 All data flow tests passed!")
        print("✅ Data validation system working correctly")
        print("✅ Resume data flows properly through all components")
        print("✅ Job data includes apply links and is validated")
        print("✅ Session management preserves data integrity")
        print("✅ JSON serialization maintains data structure")
        print("\n🚀 System is ready for efficient operation!")
    else:
        print("\n⚠️ Some tests failed. Data flow issues detected.")
    
    print("\n🌐 Ready for production use!")
    print("   All functionalities should work efficiently with validated data.")
