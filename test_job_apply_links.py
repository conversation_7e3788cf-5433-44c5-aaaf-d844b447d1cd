#!/usr/bin/env python3
"""
Test script to verify that job search results include proper apply links
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.job import search_jobs, generate_mock_jobs

def test_job_apply_links():
    """Test that job search results include apply links"""
    print("🧪 Testing Job Apply Links...")
    print("=" * 50)
    
    # Test with mock resume data
    mock_resume = {
        "Technical Skills": ["Python", "JavaScript", "React"],
        "experience": [
            {"title": "Software Engineer", "company": "Tech Corp", "duration": "2 years"}
        ],
        "education": [
            {"degree": "Computer Science", "school": "University"}
        ]
    }
    
    print("📋 Mock Resume Data:")
    print(f"   Skills: {mock_resume['Technical Skills']}")
    print(f"   Experience: {len(mock_resume['experience'])} positions")
    print()
    
    # Test job search
    print("🔍 Searching for jobs...")
    jobs = search_jobs(mock_resume)
    
    print(f"✅ Found {len(jobs)} job opportunities")
    print()
    
    # Verify apply links
    print("🔗 Checking Apply Links:")
    print("-" * 30)
    
    for i, job in enumerate(jobs[:5], 1):  # Check first 5 jobs
        title = job.get('title', 'Unknown Title')
        company = job.get('company', 'Unknown Company')
        apply_url = job.get('apply_url', '')
        url = job.get('url', '')
        location = job.get('location', 'Location not specified')
        salary = job.get('salary', 'Salary not specified')
        relevance = job.get('relevance_score', 0)
        
        print(f"{i}. {title}")
        print(f"   🏢 Company: {company}")
        print(f"   📍 Location: {location}")
        print(f"   💰 Salary: {salary}")
        print(f"   🎯 Match: {relevance}%")
        print(f"   🔗 Job URL: {url}")
        print(f"   🚀 Apply URL: {apply_url}")
        
        # Verify apply link exists
        if apply_url:
            print(f"   ✅ Apply link available")
        else:
            print(f"   ❌ Apply link missing!")
        
        print()
    
    # Test specific scenarios
    print("🧪 Testing Mock Job Generation...")
    print("-" * 30)
    
    mock_jobs = generate_mock_jobs(["Python", "Django"], "senior")
    
    for i, job in enumerate(mock_jobs[:3], 1):
        title = job.get('title', 'Unknown Title')
        company = job.get('company', 'Unknown Company')
        apply_url = job.get('apply_url', '')
        
        print(f"{i}. {title} at {company}")
        print(f"   Apply URL: {apply_url}")
        
        if apply_url and apply_url.startswith('http'):
            print(f"   ✅ Valid apply URL")
        else:
            print(f"   ❌ Invalid apply URL!")
        print()
    
    print("🎉 Test completed!")

if __name__ == "__main__":
    test_job_apply_links()
