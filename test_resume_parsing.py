#!/usr/bin/env python3
"""
Test script to verify resume parsing and storage
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_file_parser():
    """Test the file parser directly"""
    print("🧪 Testing File Parser...")
    print("=" * 50)
    
    from utils.file_parser import extract_fields
    
    # Test with sample resume text
    sample_resume_text = """
    <PERSON>
    Software Engineer
    <EMAIL>
    +1-555-123-4567
    linkedin.com/in/johndoe
    github.com/johndoe
    
    TECHNICAL SKILLS:
    Python, JavaScript, React, Node.js, SQL, AWS, Docker
    
    EXPERIENCE:
    Software Engineer at TechCorp (2020-2023)
    - Developed web applications using React and Node.js
    - Worked with Python and Django for backend services
    
    EDUCATION:
    Bachelor of Science in Computer Science
    University of Technology (2016-2020)
    """
    
    result = extract_fields(sample_resume_text)
    
    print("📋 Parsed Resume Data:")
    for key, value in result.items():
        print(f"   {key}: {value}")
    
    # Verify required fields
    required_fields = ["name", "email", "Technical Skills"]
    missing_fields = [field for field in required_fields if not result.get(field)]
    
    if missing_fields:
        print(f"❌ Missing fields: {missing_fields}")
    else:
        print("✅ All required fields extracted successfully")
    
    return result

def test_parser_node():
    """Test the parser node"""
    print("\n🧪 Testing Parser Node...")
    print("=" * 50)
    
    from agents.parser import parser_node
    from agents.manager import create_resume_state
    
    # Create sample file content
    sample_text = """John Doe
Software Engineer
<EMAIL>
Python, JavaScript, React"""
    
    sample_bytes = sample_text.encode('utf-8')
    
    # Create state
    state = create_resume_state(file_name="test_resume.txt", file_bytes=sample_bytes, session_id="test-123")
    
    print("📋 Initial State:")
    print(f"   File name: {state['file_name']}")
    print(f"   Has file bytes: {state['file_bytes'] is not None}")
    print(f"   Session ID: {state['session_id']}")
    print(f"   History: {state['history']}")
    
    # Run parser
    try:
        updated_state = parser_node(state)
        
        print("\n📋 Updated State:")
        print(f"   Parsed data: {updated_state['parsed']}")
        print(f"   History: {updated_state['history']}")
        
        if updated_state['parsed']:
            print("✅ Parser node executed successfully")
            return updated_state
        else:
            print("❌ Parser node returned empty data")
            return None
            
    except Exception as e:
        print(f"❌ Parser node failed: {e}")
        return None

def test_manager_agent():
    """Test the manager agent resume processing"""
    print("\n🧪 Testing Manager Agent...")
    print("=" * 50)
    
    from agents.manager import manager_agent
    
    # Create sample resume content
    sample_resume = """
    Jane Smith
    Senior Software Developer
    <EMAIL>
    +1-555-987-6543
    
    TECHNICAL SKILLS:
    Python, Django, React, PostgreSQL, AWS, Docker, Kubernetes
    
    EXPERIENCE:
    Senior Software Developer at InnovaTech (2021-Present)
    - Lead development of microservices architecture
    - Implemented CI/CD pipelines using Docker and Kubernetes
    
    Software Engineer at StartupCorp (2019-2021)
    - Built web applications using Django and React
    
    EDUCATION:
    Master of Science in Computer Science
    Tech University (2017-2019)
    """
    
    file_content = sample_resume.encode('utf-8')
    
    try:
        result = manager_agent.process_resume_upload(
            file_name="jane_smith_resume.txt",
            file_content=file_content,
            user_name="Jane Smith",
            email="<EMAIL>"
        )
        
        print("📋 Manager Agent Result:")
        for key, value in result.items():
            if key == "parsed" and isinstance(value, dict):
                print(f"   {key}:")
                for sub_key, sub_value in value.items():
                    print(f"      {sub_key}: {sub_value}")
            else:
                print(f"   {key}: {value}")
        
        if "error" in result:
            print(f"❌ Manager agent failed: {result['error']}")
            return None
        elif result.get("parsed"):
            print("✅ Manager agent processed resume successfully")
            return result
        else:
            print("❌ Manager agent returned no parsed data")
            return None
            
    except Exception as e:
        print(f"❌ Manager agent exception: {e}")
        return None

def test_json_serialization():
    """Test JSON serialization of parsed data"""
    print("\n🧪 Testing JSON Serialization...")
    print("=" * 50)
    
    import json
    
    sample_data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "Technical Skills": ["Python", "JavaScript", "React"],
        "experience": ["Software Engineer at TechCorp"],
        "education": ["BS Computer Science"]
    }
    
    try:
        # Test JSON serialization
        json_str = json.dumps(sample_data, indent=2)
        print("📋 JSON Serialized Data:")
        print(json_str)
        
        # Test JSON deserialization
        parsed_back = json.loads(json_str)
        print("\n📋 JSON Deserialized Data:")
        print(parsed_back)
        
        if parsed_back == sample_data:
            print("✅ JSON serialization/deserialization works correctly")
            return True
        else:
            print("❌ JSON serialization/deserialization failed")
            return False
            
    except Exception as e:
        print(f"❌ JSON serialization failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Resume Parsing Tests...")
    print("=" * 60)
    
    # Run all tests
    test1_result = test_file_parser()
    test2_result = test_parser_node()
    test3_result = test_manager_agent()
    test4_result = test_json_serialization()
    
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"   File Parser: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Parser Node: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"   Manager Agent: {'✅ PASS' if test3_result else '❌ FAIL'}")
    print(f"   JSON Serialization: {'✅ PASS' if test4_result else '❌ FAIL'}")
    
    if all([test1_result, test2_result, test3_result, test4_result]):
        print("\n🎉 All tests passed! Resume parsing should work correctly.")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")
