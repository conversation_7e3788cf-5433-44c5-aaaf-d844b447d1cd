#!/usr/bin/env python3
"""
Simple test to verify LLM-based parsing works
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.file_parser import parse_resume
import json

def test_llm_parser():
    """Test the LLM-based parser with <PERSON><PERSON><PERSON>'s resume"""
    print("🧪 Testing LLM-based Parser...")
    print("=" * 50)
    
    try:
        # Read <PERSON><PERSON>'s resume
        with open("Pratham_resume_AI.pdf", "rb") as f:
            file_bytes = f.read()
        
        print(f"📄 File size: {len(file_bytes)} bytes")
        
        # Parse with LLM
        result = parse_resume("Pratham_resume_AI.pdf", file_bytes)
        
        print("\n📋 Parsing Results:")
        print(f"   Name: {result.get('name', 'NOT FOUND')}")
        print(f"   Email: {result.get('email', 'NOT FOUND')}")
        print(f"   Phone: {result.get('phone', 'NOT FOUND')}")
        print(f"   LinkedIn: {result.get('linkedin', 'NOT FOUND')}")
        print(f"   GitHub: {result.get('github', 'NOT FOUND')}")
        
        # Check projects
        projects = result.get('projects', [])
        print(f"\n📊 Projects: {len(projects)} found")
        
        if projects:
            for i, project in enumerate(projects[:2]):
                print(f"   Project {i+1}:")
                if isinstance(project, dict):
                    print(f"     Title: {project.get('title', 'N/A')}")
                    print(f"     Description: {project.get('description', 'N/A')[:100]}...")
                    print(f"     Tech: {project.get('tech', [])}")
                else:
                    print(f"     Raw: {project}")
        
        # Check work experience
        work_exp = result.get('work_experience', [])
        print(f"\n💼 Work Experience: {len(work_exp)} found")
        
        if work_exp:
            for i, exp in enumerate(work_exp):
                print(f"   Experience {i+1}:")
                if isinstance(exp, dict):
                    print(f"     Company: {exp.get('company', 'N/A')}")
                    print(f"     Role: {exp.get('role', 'N/A')}")
                    print(f"     Description: {exp.get('description', 'N/A')[:100]}...")
                else:
                    print(f"     Raw: {exp}")
        
        # Check technical skills
        tech_skills = result.get('technical_skills', {})
        print(f"\n🔧 Technical Skills:")
        if isinstance(tech_skills, dict):
            for category, skills in tech_skills.items():
                print(f"   {category}: {skills}")
        else:
            print(f"   Raw: {tech_skills}")
        
        # Check if parsing was successful
        if result.get('name') and result.get('email'):
            print("\n✅ LLM parsing successful!")
            return True
        else:
            print("\n❌ LLM parsing failed - missing basic info")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_llm_parser()
    if success:
        print("\n🎉 Test passed!")
    else:
        print("\n💥 Test failed!")
