#!/usr/bin/env python3
"""
Test to verify direct apply links are working
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.job import generate_india_focused_mock_jobs
import json

def test_direct_apply_links():
    """Test that jobs have direct apply links, not just career page links"""
    print("🔗 Testing Direct Apply Links...")
    print("=" * 60)
    
    # Sample tech skills
    tech_skills = ["Python", "Machine Learning", "Deep Learning", "NLP"]
    experience_level = "Entry"
    
    print(f"📋 Generating jobs for: {tech_skills}")
    
    # Generate India-focused jobs
    india_jobs = generate_india_focused_mock_jobs(tech_skills, experience_level)
    
    print(f"\n📊 Analyzing {len(india_jobs)} job apply links:")
    print("=" * 80)
    
    direct_apply_count = 0
    career_page_count = 0
    
    for i, job in enumerate(india_jobs, 1):
        title = job.get('title', 'N/A')
        company = job.get('company', 'N/A')
        apply_url = job.get('apply_url', 'N/A')
        job_url = job.get('url', 'N/A')
        job_id = job.get('job_id', 'N/A')
        posted_date = job.get('posted_date', 'N/A')
        
        print(f"\n🏢 Job {i}: {title} at {company}")
        print(f"   Job ID: {job_id}")
        print(f"   Posted: {posted_date}")
        print(f"   Job URL: {job_url}")
        print(f"   Apply URL: {apply_url}")
        
        # Check if it's a direct apply link or career page
        is_direct_apply = (
            apply_url != job_url and  # Different from job URL
            'apply' in apply_url.lower() and  # Contains 'apply'
            not apply_url.endswith('/careers') and  # Not just careers page
            not apply_url.endswith('/careers/') and
            'jobs/apply' in apply_url.lower() or 'apply/' in apply_url.lower()
        )
        
        if is_direct_apply:
            direct_apply_count += 1
            print(f"   ✅ DIRECT APPLY LINK")
        else:
            career_page_count += 1
            print(f"   ❌ CAREER PAGE LINK")
        
        # Check location
        location = job.get('location', 'N/A')
        indian_cities = ['Noida', 'Mumbai', 'Gurgaon', 'Bengaluru', 'Pune', 'Hyderabad', 'Chennai']
        is_india_location = any(city in location for city in indian_cities) or 'India' in location
        print(f"   📍 Location: {location} {'✅' if is_india_location else '❌'}")
        
        # Check salary in INR
        salary = job.get('salary', 'N/A')
        is_inr_salary = '₹' in salary
        print(f"   💰 Salary: {salary} {'✅' if is_inr_salary else '❌'}")
    
    # Summary analysis
    print("\n" + "=" * 80)
    print("📈 APPLY LINKS ANALYSIS:")
    
    total_jobs = len(india_jobs)
    direct_apply_percentage = (direct_apply_count / total_jobs) * 100
    career_page_percentage = (career_page_count / total_jobs) * 100
    
    print(f"   Total Jobs: {total_jobs}")
    print(f"   Direct Apply Links: {direct_apply_count}/{total_jobs} ({direct_apply_percentage:.1f}%)")
    print(f"   Career Page Links: {career_page_count}/{total_jobs} ({career_page_percentage:.1f}%)")
    
    # Quality assessment
    if direct_apply_percentage >= 80:
        print(f"   ✅ EXCELLENT: Most jobs have direct apply links")
    elif direct_apply_percentage >= 60:
        print(f"   ⚠️ GOOD: Majority have direct apply links")
    else:
        print(f"   ❌ NEEDS IMPROVEMENT: Too many career page links")
    
    # Check for specific features
    print(f"\n🔍 ADDITIONAL FEATURES:")
    jobs_with_job_id = sum(1 for job in india_jobs if job.get('job_id') and job.get('job_id') != 'N/A')
    jobs_with_posted_date = sum(1 for job in india_jobs if job.get('posted_date') and job.get('posted_date') != 'N/A')
    jobs_with_inr_salary = sum(1 for job in india_jobs if '₹' in job.get('salary', ''))
    
    print(f"   Jobs with Job ID: {jobs_with_job_id}/{total_jobs}")
    print(f"   Jobs with Posted Date: {jobs_with_posted_date}/{total_jobs}")
    print(f"   Jobs with INR Salary: {jobs_with_inr_salary}/{total_jobs}")
    
    # Show some example direct apply links
    print(f"\n🔗 EXAMPLE DIRECT APPLY LINKS:")
    direct_apply_jobs = [job for job in india_jobs if 'apply' in job.get('apply_url', '').lower()]
    for i, job in enumerate(direct_apply_jobs[:3], 1):
        print(f"   {i}. {job.get('company')}: {job.get('apply_url')}")
    
    return india_jobs

def main():
    """Main test function"""
    print("🚀 Testing Direct Apply Links...")
    print("=" * 50)
    
    jobs = test_direct_apply_links()
    
    print("\n" + "=" * 60)
    print("🎯 KEY IMPROVEMENTS:")
    print("   ✅ Direct job application links (not career pages)")
    print("   ✅ Job IDs for tracking")
    print("   ✅ Posted dates for freshness")
    print("   ✅ Specific job URLs")
    print("   ✅ India locations with cities")
    print("   ✅ Salary ranges in INR")
    
    print("\n🎉 Direct apply links test completed!")

if __name__ == "__main__":
    main()
