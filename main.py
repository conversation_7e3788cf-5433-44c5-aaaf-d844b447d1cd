from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Form, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import Optional
from agents.manager import manager_agent
from utils.db import session_manager
from utils.auth import auth_manager, User<PERSON><PERSON>, UserLogin, Token
from datetime import timedelta
import io
import os

# Load environment variables from .env file
def load_env():
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"')
        print("✅ Environment variables loaded")
    except FileNotFoundError:
        print("⚠️ .env file not found, using system environment variables")
    except Exception as e:
        print(f"⚠️ Error loading .env file: {e}")

# Load environment variables at startup
load_env()

app = FastAPI(title="Resume Intelligence Assistant API", version="1.0.0")

# Security
security = HTTPBearer()

# Allow CORS (modify for production)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # You can replace "*" with your frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dependency to get current user from JWT token
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user from JWT token"""
    token = credentials.credentials
    user = auth_manager.verify_token(token)

    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user

# Pydantic models for request/response
class UserQueryRequest(BaseModel):
    user_input: str
    session_id: Optional[str] = None
    user_name: Optional[str] = None

class ChatResponse(BaseModel):
    message: str
    session_id: Optional[str] = None
    data: Optional[dict] = None
    error: Optional[str] = None

@app.get("/")
async def root():
    return {"message": "Resume Intelligence Assistant API is running!"}

# Authentication endpoints
@app.post("/auth/signup", response_model=Token)
async def signup(user_data: UserCreate):
    """
    Create a new user account
    """
    try:
        user = auth_manager.create_user(
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name
        )

        # Create access token
        from utils.auth import ACCESS_TOKEN_EXPIRE_MINUTES
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = auth_manager.create_access_token(
            data={"sub": user["email"]}, expires_delta=access_token_expires
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": user
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/auth/login", response_model=Token)
async def login(user_credentials: UserLogin):
    """
    Authenticate user and return JWT token
    """
    user = auth_manager.authenticate_user(
        email=user_credentials.email,
        password=user_credentials.password
    )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    from utils.auth import ACCESS_TOKEN_EXPIRE_MINUTES
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = auth_manager.create_access_token(
        data={"sub": user["email"]}, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user
    }

@app.get("/auth/me")
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """
    Get current user information
    """
    return current_user

@app.post("/upload_resume")
async def upload_resume(
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Upload resume and create new session. Manager agent processes the resume.
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith(('.pdf', '.docx')):
            raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported")

        # Read file content
        content = await file.read()

        # Use authenticated user's information
        user_name = current_user["full_name"]
        email = current_user["email"]

        # Process through manager agent
        result = manager_agent.process_resume_upload(
            file_name=file.filename,
            file_content=content,
            user_name=user_name,
            email=email
        )

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        # Link session to user account
        auth_manager.add_session_to_user(email, result["session_id"])

        return {
            "message": result["message"],
            "session_id": result["session_id"],
            "user_name": user_name,
            "parsed": result["parsed"],
            "improvement": result["improvement"],
            "jobs": result["jobs"]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing resume: {str(e)}")

@app.post("/chat")
async def chat_with_manager(
    request: UserQueryRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Main chat endpoint - all user interactions go through the manager agent
    """
    try:
        # Get user's sessions if no specific session_id provided
        if not request.session_id:
            user_sessions = auth_manager.get_user_sessions(current_user["email"])
            if user_sessions:
                request.session_id = user_sessions[-1]  # Use most recent session

        # Process user request through manager agent
        result = manager_agent.process_user_request(
            user_input=request.user_input,
            session_id=request.session_id,
            user_name=current_user["full_name"]
        )

        if "error" in result:
            return ChatResponse(
                message="Sorry, I encountered an error.",
                error=result["error"]
            )

        return ChatResponse(
            message=result.get("message", "Task completed"),
            session_id=result.get("session_id"),
            data={
                "parsed": result.get("parsed"),
                "improvements": result.get("improvements"),
                "jobs": result.get("jobs"),
                "action_required": result.get("action_required")
            }
        )

    except Exception as e:
        return ChatResponse(
            message="Sorry, I encountered an error processing your request.",
            error=str(e)
        )

@app.get("/session/{session_id}")
async def get_session_data(session_id: str):
    """
    Get session data by session ID
    """
    try:
        session = session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        return {
            "session_id": session_id,
            "user_name": session.get("user_name"),
            "email": session.get("email"),
            "created_at": session.get("created_at"),
            "last_accessed": session.get("last_accessed"),
            "parsed": session.get("resume_data", {}).get("parsed", {}),
            "improvement_suggestions": session.get("improvement_suggestions", []),
            "job_recommendations": session.get("job_recommendations", []),
            "conversation_history": session.get("conversation_history", [])
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/user/{user_name}/session")
async def get_user_session(user_name: str):
    """
    Get the most recent session for a user by name
    """
    try:
        session = session_manager.get_session_by_name(user_name)
        if not session:
            raise HTTPException(status_code=404, detail=f"No session found for user '{user_name}'")

        session_id = session.get("session_id")
        return {
            "session_id": session_id,
            "user_name": session.get("user_name"),
            "email": session.get("email"),
            "created_at": session.get("created_at"),
            "last_accessed": session.get("last_accessed"),
            "parsed": session.get("resume_data", {}).get("parsed", {}),
            "improvement_suggestions": session.get("improvement_suggestions", []),
            "job_recommendations": session.get("job_recommendations", []),
            "conversation_history": session.get("conversation_history", [])
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/recommend_jobs")
async def recommend_jobs(session_id: Optional[str] = None, user_name: Optional[str] = None):
    """
    Legacy endpoint for job recommendations - redirects to manager agent
    """
    try:
        result = manager_agent.process_user_request(
            user_input="Find job recommendations for me",
            session_id=session_id,
            user_name=user_name
        )

        if "error" in result:
            raise HTTPException(status_code=404, detail=result["error"])

        return {
            "message": result["message"],
            "jobs": result.get("jobs", []),
            "parsed": result.get("parsed", {}),
            "improvement": result.get("improvements", [])
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Simple upload endpoint without authentication
@app.post("/upload_resume_simple")
async def upload_resume_simple(file: UploadFile = File(...)):
    """Upload and process resume without authentication"""
    try:
        # Validate file type (allow .txt for testing)
        if not file.filename.lower().endswith(('.pdf', '.docx', '.txt')):
            raise HTTPException(status_code=400, detail="Only PDF, DOCX, and TXT files are supported")

        # Read file content
        content = await file.read()

        # Debug: Print file info
        print(f"🔍 File info: {file.filename}, size: {len(content)} bytes")
        print(f"🔍 Content preview: {content[:200]}...")

        # Process the resume using the manager agent
        result = manager_agent.process_resume_upload(
            file_name=file.filename,
            file_content=content,
            user_name="Demo User",
            email="<EMAIL>"
        )

        # Debug: Print what we got from manager agent
        print(f"🔍 Manager agent result keys: {list(result.keys())}")
        print(f"🔍 Parsed data: {result.get('parsed', {})}")
        print(f"🔍 Jobs count: {len(result.get('jobs', []))}")

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        response_data = {
            "session_id": result.get("session_id"),
            "message": "Resume processed successfully",
            "parsed": result.get("parsed", {}),
            "improvement": result.get("improvement", []),
            "jobs": result.get("jobs", [])
        }

        # Debug: Print what we're returning
        print(f"🔍 API response keys: {list(response_data.keys())}")
        print(f"🔍 API response parsed: {response_data['parsed']}")

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing resume: {str(e)}")

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Resume Intelligence Assistant"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
