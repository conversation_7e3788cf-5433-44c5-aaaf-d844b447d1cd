from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
from agents.manager import manager_agent
import io
import os

# Load environment variables from .env file
def load_env():
    try:
        with open('.env', 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
        print("✅ Environment variables loaded")
    except FileNotFoundError:
        print("⚠️ .env file not found")
    except Exception as e:
        print(f"⚠️ Error loading .env: {e}")

load_env()

app = FastAPI(title="Resume Intelligence Assistant API", version="1.0.0")

# Allow CORS (modify for production)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # You can replace "*" with your frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class ResumeUploadResponse(BaseModel):
    message: str
    session_id: str
    parsed: Optional[dict] = None  # Changed to match frontend expectation

class JobSearchRequest(BaseModel):
    session_id: str

class JobSearchResponse(BaseModel):
    message: str
    jobs: list
    session_id: str

class ResumeImprovementRequest(BaseModel):
    session_id: str

class ResumeImprovementResponse(BaseModel):
    message: str
    improvements: dict
    session_id: str

# Health check endpoint
@app.get("/")
async def root():
    return {"message": "Resume Intelligence Assistant API is running!"}

# Resume upload and parsing endpoint
@app.post("/upload-resume", response_model=ResumeUploadResponse)
async def upload_resume(file: UploadFile = File(...)):
    """
    Upload and parse a resume file (PDF or DOCX)
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith(('.pdf', '.docx')):
            raise HTTPException(status_code=400, detail="Only PDF and DOCX files are supported")

        # Read file content
        file_content = await file.read()

        # Extract user name from filename (fallback)
        user_name = file.filename.split('.')[0].replace('_', ' ').replace('-', ' ').title()

        # Use direct parser instead of complex workflow (temporary fix)
        from utils.file_parser import parse_resume
        import uuid

        try:
            # Parse resume directly
            parsed_data = parse_resume(file.filename, file_content)

            # Create session ID
            session_id = str(uuid.uuid4())

            # Create successful result
            result = {
                "message": f"Welcome {user_name}! Your resume has been processed successfully.",
                "session_id": session_id,
                "parsed": parsed_data
            }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error parsing resume: {str(e)}")

        return ResumeUploadResponse(
            message=result["message"],
            session_id=result["session_id"],
            parsed=result.get("parsed")  # Now matches the response model
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing resume: {str(e)}")

# Job search endpoint
@app.post("/search-jobs", response_model=JobSearchResponse)
async def search_jobs(request: JobSearchRequest):
    """
    Search for job opportunities based on parsed resume
    """
    try:
        result = manager_agent.search_jobs(request.session_id)

        return JobSearchResponse(
            message=result["message"],
            jobs=result["jobs"],
            session_id=result["session_id"]
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching jobs: {str(e)}")

# Resume improvement endpoint
@app.post("/improve-resume", response_model=ResumeImprovementResponse)
async def improve_resume(request: ResumeImprovementRequest):
    """
    Get resume improvement suggestions
    """
    try:
        result = manager_agent.improve_resume(request.session_id)

        return ResumeImprovementResponse(
            message=result["message"],
            improvements=result["improvements"],
            session_id=result["session_id"]
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error improving resume: {str(e)}")

# Get session info endpoint
@app.get("/session/{session_id}")
async def get_session_info(session_id: str):
    """
    Get information about a session
    """
    try:
        # Simple session info without authentication
        return {
            "session_id": session_id,
            "status": "active",
            "message": "Session information retrieved"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving session: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
