#!/usr/bin/env python3
"""
Direct test of the API to verify resume parsing and data storage
"""

import sys
import os
import requests
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_with_real_resume():
    """Test API with a real resume file"""
    print("🧪 Testing API with Real Resume...")
    print("=" * 50)
    
    # Create a sample resume content that should parse well
    resume_content = """
<PERSON>
Senior Software Engineer
<EMAIL>
+1-555-123-4567
linkedin.com/in/johnsmith
github.com/johnsmith

TECHNICAL SKILLS:
Python, JavaScript, React, Node.js, Django, Flask, PostgreSQL, MySQL, AWS, Docker, Kubernetes, Git

PROFESSIONAL EXPERIENCE:

Senior Software Engineer | TechCorp Inc. | 2021 - Present
• Led development of microservices architecture using Python and Django
• Built responsive web applications with React and Node.js
• Deployed applications on AWS using Docker and Kubernetes
• Collaborated with cross-functional teams to deliver high-quality software

Software Engineer | StartupXYZ | 2019 - 2021
• Developed REST APIs using Python and Flask
• Worked with PostgreSQL and MySQL databases
• Implemented CI/CD pipelines using Git and Docker

EDUCATION:
Master of Science in Computer Science | Tech University | 2017-2019
Bachelor of Science in Computer Science | State University | 2015-2017

PROJECTS:
• E-commerce Platform: Built using Django, React, and PostgreSQL
• Task Management App: Developed with Node.js and MongoDB
• Data Analytics Dashboard: Created using Python and React
"""
    
    try:
        # Test API health first
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ API server is healthy")
        else:
            print("❌ API server health check failed")
            return False
            
    except Exception as e:
        print(f"❌ Cannot connect to API server: {e}")
        return False
    
    try:
        # Create a proper file upload (use .txt for testing)
        files = {
            'file': ('john_smith_resume.txt', resume_content.encode('utf-8'), 'text/plain')
        }
        
        print("📤 Uploading resume to API...")
        response = requests.post(
            "http://localhost:8000/upload_resume_simple",
            files=files,
            timeout=30
        )
        
        print(f"📥 API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API call successful!")
            print(f"Response keys: {list(result.keys())}")
            
            # Check each part of the response
            session_id = result.get('session_id')
            message = result.get('message')
            parsed_data = result.get('parsed', {})
            improvement = result.get('improvement', [])
            jobs = result.get('jobs', [])
            
            print(f"\n📋 Response Analysis:")
            print(f"   Session ID: {session_id}")
            print(f"   Message: {message}")
            print(f"   Parsed data keys: {list(parsed_data.keys()) if parsed_data else 'None'}")
            print(f"   Improvements count: {len(improvement)}")
            print(f"   Jobs count: {len(jobs)}")
            
            if parsed_data:
                print(f"\n📄 Parsed Resume Data:")
                for key, value in parsed_data.items():
                    if isinstance(value, list):
                        print(f"   {key}: {len(value)} items - {value[:3]}{'...' if len(value) > 3 else ''}")
                    else:
                        print(f"   {key}: {value}")
                
                # Check required fields
                required_fields = ['name', 'email', 'Technical Skills']
                missing_fields = [field for field in required_fields if not parsed_data.get(field)]
                
                if missing_fields:
                    print(f"⚠️ Missing required fields: {missing_fields}")
                else:
                    print("✅ All required fields present")
                
                return True
            else:
                print("❌ No parsed data in response")
                return False
                
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def test_manager_agent_directly():
    """Test the manager agent directly"""
    print("\n🧪 Testing Manager Agent Directly...")
    print("=" * 50)
    
    from agents.manager import manager_agent
    
    # Create sample resume content
    resume_content = """
Jane Doe
Full Stack Developer
<EMAIL>
+1-555-987-6543

TECHNICAL SKILLS:
Python, JavaScript, React, Vue.js, Django, FastAPI, PostgreSQL, MongoDB, AWS, Docker

EXPERIENCE:
Full Stack Developer at InnovaTech (2020-Present)
- Developed web applications using React and Django
- Worked with PostgreSQL and MongoDB databases

EDUCATION:
Bachelor of Science in Computer Science
Tech University (2016-2020)
"""
    
    try:
        result = manager_agent.process_resume_upload(
            file_name="jane_doe_resume.txt",
            file_content=resume_content.encode('utf-8'),
            user_name="Jane Doe",
            email="<EMAIL>"
        )
        
        print("📋 Manager Agent Result:")
        for key, value in result.items():
            if key == "parsed" and isinstance(value, dict):
                print(f"   {key}:")
                for sub_key, sub_value in value.items():
                    if isinstance(sub_value, list):
                        print(f"      {sub_key}: {len(sub_value)} items - {sub_value[:2]}{'...' if len(sub_value) > 2 else ''}")
                    else:
                        print(f"      {sub_key}: {sub_value}")
            elif isinstance(value, list):
                print(f"   {key}: {len(value)} items")
            else:
                print(f"   {key}: {value}")
        
        if "error" in result:
            print(f"❌ Manager agent error: {result['error']}")
            return False
        elif result.get("parsed"):
            print("✅ Manager agent processed resume successfully")
            
            # Verify data format
            parsed = result["parsed"]
            if parsed.get("name") and parsed.get("Technical Skills"):
                print("✅ Required data fields present")
                return True
            else:
                print("❌ Missing required data fields")
                return False
        else:
            print("❌ Manager agent returned no parsed data")
            return False
            
    except Exception as e:
        print(f"❌ Manager agent test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing API and Resume Parsing...")
    print("=" * 60)
    
    test1_result = test_api_with_real_resume()
    test2_result = test_manager_agent_directly()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   API Test: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Manager Agent Test: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 All tests passed! Resume parsing and API are working correctly.")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")
