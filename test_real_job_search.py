#!/usr/bin/env python3
"""
Test real job search using <PERSON><PERSON>
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.job import search_jobs
import json

def test_real_job_search():
    """Test real job search with <PERSON><PERSON>"""
    print("🔍 Testing REAL Job Search with Tavily...")
    print("=" * 60)
    
    # Sample parsed resume data
    sample_resume = {
        "name": "Pratham Dixit",
        "email": "<EMAIL>",
        "phone": "+91-6307803699",
        "location": "Lucknow, Uttar Pradesh",
        "total_years_experience": 0.25,
        "experience_level": "Entry",
        "primary_skill_domain": "AI/ML",
        "target_job_titles": ["ML Engineer", "AI Engineer", "Software Engineer"],
        "Technical Skills": [
            "Python", "Machine Learning", "Deep Learning", "NLP", 
            "Computer Vision", "TensorFlow", "PyTorch", "Langchain",
            "FastAPI", "Flask", "SQL", "Git"
        ]
    }
    
    print(f"📋 Searching for real jobs:")
    print(f"   Skills: {sample_resume['Technical Skills'][:3]}")
    print(f"   Experience: {sample_resume['experience_level']}")
    print(f"   Location: India")
    
    try:
        print("\n🌐 Executing Tavily web search for real job openings...")
        
        # Run real job search
        jobs = search_jobs(sample_resume)
        
        print(f"\n📊 Real Job Search Results:")
        print(f"   Total jobs found: {len(jobs)}")
        
        if not jobs:
            print("   ❌ No real jobs found")
            return []
        
        # Analyze the jobs
        real_jobs = 0
        india_jobs = 0
        working_links = 0
        
        print(f"\n💼 Real Job Analysis:")
        print("=" * 50)
        
        for i, job in enumerate(jobs[:5], 1):  # Show first 5 jobs
            title = job.get('title', 'N/A')
            company = job.get('company', 'N/A')
            location = job.get('location', 'N/A')
            salary = job.get('salary', 'N/A')
            apply_url = job.get('apply_url', 'N/A')
            source = job.get('source', 'N/A')
            relevance = job.get('relevance_score', 0)
            is_real = job.get('verified_real', False)
            
            print(f"\n🏢 Job {i}: {title}")
            print(f"   Company: {company}")
            print(f"   Location: {location}")
            print(f"   Salary: {salary}")
            print(f"   Source: {source}")
            print(f"   Relevance: {relevance}%")
            print(f"   Apply URL: {apply_url[:60]}...")
            
            # Check if it's a real job
            is_real_job = is_real or job.get('search_result', False)
            is_india_job = 'india' in location.lower() or any(city in location.lower() for city in ['noida', 'mumbai', 'gurgaon', 'bengaluru', 'pune', 'hyderabad', 'chennai'])
            is_working_link = apply_url and apply_url != 'N/A' and not apply_url.startswith('https://fake')
            
            if is_real_job:
                real_jobs += 1
            if is_india_job:
                india_jobs += 1
            if is_working_link:
                working_links += 1
            
            print(f"   ✅ Real Job: {'Yes' if is_real_job else 'No'}")
            print(f"   ✅ India Location: {'Yes' if is_india_job else 'No'}")
            print(f"   ✅ Working Link: {'Yes' if is_working_link else 'No'}")
        
        # Summary
        total_analyzed = min(len(jobs), 5)
        print(f"\n📈 REAL JOB SEARCH QUALITY:")
        print(f"   Real Jobs: {real_jobs}/{total_analyzed} ({real_jobs/total_analyzed*100:.1f}%)")
        print(f"   India Jobs: {india_jobs}/{total_analyzed} ({india_jobs/total_analyzed*100:.1f}%)")
        print(f"   Working Links: {working_links}/{total_analyzed} ({working_links/total_analyzed*100:.1f}%)")
        
        # Overall quality score
        quality_score = (real_jobs + india_jobs + working_links) / (total_analyzed * 3) * 100
        print(f"   Overall Quality: {quality_score:.1f}%")
        
        if quality_score >= 80:
            print(f"   ✅ EXCELLENT: High-quality real job results")
        elif quality_score >= 60:
            print(f"   ⚠️ GOOD: Decent real job results")
        else:
            print(f"   ❌ NEEDS IMPROVEMENT: Poor job quality")
        
        # Show example real job
        if jobs:
            print(f"\n🔗 EXAMPLE REAL JOB:")
            example_job = jobs[0]
            print(f"   Title: {example_job.get('title')}")
            print(f"   Company: {example_job.get('company')}")
            print(f"   Apply URL: {example_job.get('apply_url')}")
            print(f"   Source: {example_job.get('source')}")
            
            # Check if URL looks real
            url = example_job.get('apply_url', '')
            if 'linkedin.com' in url or 'naukri.com' in url or 'careers.' in url:
                print(f"   ✅ URL looks like a real job portal")
            else:
                print(f"   ⚠️ URL may not be a real job posting")
        
        return jobs
        
    except Exception as e:
        print(f"❌ Error during real job search: {e}")
        return []

def main():
    """Main test function"""
    print("🚀 Testing Real Job Search with Tavily...")
    print("=" * 50)
    
    jobs = test_real_job_search()
    
    print("\n" + "=" * 60)
    print("🎯 REAL JOB SEARCH STATUS:")
    
    if jobs:
        real_job_count = sum(1 for job in jobs if job.get('verified_real') or job.get('search_result'))
        print(f"   ✅ Found {len(jobs)} job results")
        print(f"   ✅ Real jobs: {real_job_count}")
        print(f"   ✅ Using Tavily web search")
        print(f"   ✅ India-focused search queries")
        print(f"   ✅ Working apply links")
    else:
        print(f"   ❌ No real jobs found")
        print(f"   ⚠️ Check Tavily API key")
        print(f"   ⚠️ Check internet connection")
    
    print("\n📱 NEXT STEPS:")
    print("   1. Verify Tavily API key is working")
    print("   2. Test apply links manually")
    print("   3. Check job posting freshness")
    print("   4. Verify India location filtering")
    
    print("\n🎉 Real job search test completed!")

if __name__ == "__main__":
    main()
