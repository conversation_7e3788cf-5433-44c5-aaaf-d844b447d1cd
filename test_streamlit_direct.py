#!/usr/bin/env python3
"""
Test the direct processing functionality that will be used in Streamlit
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_direct_processing():
    """Test direct processing with manager agent"""
    print("🧪 Testing Direct Processing...")
    print("=" * 50)
    
    from agents.manager import manager_agent
    
    # Create sample resume content
    resume_content = """
<PERSON>
Senior Full Stack Developer
<EMAIL>
+1-555-123-4567
linkedin.com/in/sarahjohnson
github.com/sarahjohnson

TECHNICAL SKILLS:
Python, JavaScript, TypeScript, React, Vue.js, Node.js, Django, FastAPI, 
PostgreSQL, MongoDB, Redis, AWS, Docker, Kubernetes, Git, Jenkins

PROFESSIONAL EXPERIENCE:

Senior Full Stack Developer | TechCorp Inc. | 2021 - Present
• Led development of microservices architecture using Python and Django
• Built responsive web applications with React and TypeScript
• Deployed applications on AWS using Docker and Kubernetes
• Mentored junior developers and conducted code reviews

Full Stack Developer | InnovateTech | 2019 - 2021
• Developed REST APIs using Node.js and Express
• Created dynamic user interfaces with Vue.js
• Worked with PostgreSQL and MongoDB databases
• Implemented CI/CD pipelines using Jenkins and Docker

Software Engineer | StartupXYZ | 2017 - 2019
• Built web applications using Python and Django
• Developed frontend components with JavaScript and React
• Optimized database queries and improved application performance

EDUCATION:
Master of Science in Computer Science | Tech University | 2015-2017
Bachelor of Science in Software Engineering | State University | 2011-2015

PROJECTS:
• E-commerce Platform: Full-stack application using Django, React, and PostgreSQL
• Task Management System: Built with Node.js, Vue.js, and MongoDB
• Real-time Chat Application: Developed using WebSockets and Redis
• Data Visualization Dashboard: Created with Python, React, and D3.js

CERTIFICATIONS:
• AWS Certified Solutions Architect
• Google Cloud Professional Developer
• MongoDB Certified Developer
"""
    
    try:
        # Simulate file upload
        file_content = resume_content.encode('utf-8')
        file_name = "sarah_johnson_resume.txt"
        
        print(f"📄 Processing resume: {file_name}")
        print(f"📊 File size: {len(file_content)} bytes")
        
        # Process with manager agent (same as Streamlit will do)
        result = manager_agent.process_resume_upload(
            file_name=file_name,
            file_content=file_content,
            user_name="Demo User",
            email="<EMAIL>"
        )
        
        print("\n📋 Processing Result:")
        print(f"   Success: {'error' not in result}")
        print(f"   Session ID: {result.get('session_id', 'Missing')}")
        print(f"   Message: {result.get('message', 'Missing')}")
        
        # Check parsed data
        parsed_data = result.get("parsed", {})
        if parsed_data:
            print(f"\n✅ Resume Data Parsed Successfully:")
            print(f"   Name: {parsed_data.get('name', 'Not found')}")
            print(f"   Email: {parsed_data.get('email', 'Not found')}")
            print(f"   Phone: {parsed_data.get('phone', 'Not found')}")
            
            skills = parsed_data.get('Technical Skills', [])
            print(f"   Skills ({len(skills)}): {', '.join(skills[:5])}{'...' if len(skills) > 5 else ''}")
            
            experience = parsed_data.get('experience', [])
            print(f"   Experience ({len(experience)} entries): {experience[:2] if experience else 'None'}")
            
            education = parsed_data.get('education', [])
            print(f"   Education ({len(education)} entries): {education[:2] if education else 'None'}")
            
            # Check jobs
            jobs = result.get("jobs", [])
            print(f"\n💼 Job Recommendations: {len(jobs)} jobs found")
            if jobs:
                first_job = jobs[0]
                print(f"   Sample job: {first_job.get('title', 'Unknown')} at {first_job.get('company', 'Unknown')}")
                print(f"   Apply URL: {first_job.get('apply_url', 'Missing')}")
                
                # Check that all jobs have apply URLs
                jobs_with_apply = sum(1 for job in jobs if job.get('apply_url'))
                print(f"   Jobs with apply links: {jobs_with_apply}/{len(jobs)}")
            
            # Check improvements
            improvements = result.get("improvement", [])
            print(f"\n📈 Improvement Suggestions: {len(improvements)} suggestions")
            if improvements:
                print(f"   Sample: {improvements[0]}")
            
            # Simulate what Streamlit will store
            session_data = {
                "session_id": result.get("session_id"),
                "resume_data": parsed_data
            }
            
            print(f"\n💾 Session Data Format:")
            print(f"   Session ID: {session_data['session_id']}")
            print(f"   Resume data keys: {list(session_data['resume_data'].keys())}")
            
            # Test JSON serialization (what Streamlit uses)
            import json
            try:
                json_str = json.dumps(session_data['resume_data'])
                json_back = json.loads(json_str)
                print(f"   JSON serialization: ✅ Success")
                
                # Verify data integrity
                if json_back == session_data['resume_data']:
                    print(f"   Data integrity: ✅ Preserved")
                    return True
                else:
                    print(f"   Data integrity: ❌ Lost")
                    return False
                    
            except Exception as e:
                print(f"   JSON serialization: ❌ Failed - {e}")
                return False
            
        else:
            print("❌ No parsed data returned")
            return False
            
    except Exception as e:
        print(f"❌ Direct processing failed: {e}")
        return False

def test_job_search_with_parsed_data():
    """Test job search with parsed resume data"""
    print("\n🧪 Testing Job Search with Parsed Data...")
    print("=" * 50)
    
    from agents.job import search_jobs
    
    # Sample parsed resume data (what Streamlit will have)
    parsed_resume = {
        "name": "Sarah Johnson",
        "email": "<EMAIL>",
        "Technical Skills": ["Python", "JavaScript", "React", "Django", "AWS", "Docker"],
        "experience": [
            "Senior Full Stack Developer at TechCorp",
            "Full Stack Developer at InnovateTech"
        ],
        "education": [
            "Master of Science in Computer Science",
            "Bachelor of Science in Software Engineering"
        ]
    }
    
    try:
        print(f"📋 Input Resume Data:")
        print(f"   Name: {parsed_resume['name']}")
        print(f"   Skills: {parsed_resume['Technical Skills']}")
        print(f"   Experience: {len(parsed_resume['experience'])} entries")
        
        # Search for jobs
        jobs = search_jobs(parsed_resume)
        
        print(f"\n💼 Job Search Results:")
        print(f"   Jobs found: {len(jobs)}")
        
        if jobs:
            # Check apply links
            jobs_with_apply = sum(1 for job in jobs if job.get('apply_url'))
            print(f"   Jobs with apply links: {jobs_with_apply}/{len(jobs)}")
            
            # Show sample jobs
            print(f"\n📋 Sample Jobs:")
            for i, job in enumerate(jobs[:3], 1):
                print(f"   {i}. {job.get('title', 'Unknown')} at {job.get('company', 'Unknown')}")
                print(f"      Apply: {job.get('apply_url', 'Missing')}")
                print(f"      Location: {job.get('location', 'Not specified')}")
                print(f"      Match: {job.get('relevance_score', 0)}%")
            
            return True
        else:
            print("❌ No jobs found")
            return False
            
    except Exception as e:
        print(f"❌ Job search failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Streamlit Direct Processing...")
    print("=" * 60)
    
    test1_result = test_direct_processing()
    test2_result = test_job_search_with_parsed_data()
    
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"   Direct Processing: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Job Search: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 All tests passed!")
        print("✅ Resume parsing works correctly")
        print("✅ Data is stored in proper JSON format")
        print("✅ Job search provides apply links")
        print("✅ Streamlit app should work perfectly!")
    else:
        print("\n⚠️ Some tests failed. Check the output above.")
    
    print("\n🌐 Ready to test in Streamlit!")
    print("   Run: streamlit run app.py")
