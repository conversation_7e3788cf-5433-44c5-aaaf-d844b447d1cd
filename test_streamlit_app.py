#!/usr/bin/env python3
"""
Test script to verify Streamlit app functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_job_generation():
    """Test job generation functionality"""
    print("🧪 Testing Job Generation...")
    print("=" * 50)
    
    from agents.job import generate_mock_jobs
    
    # Test with different skill sets
    test_cases = [
        (["Python", "Django"], "senior"),
        (["JavaScript", "React"], "mid"),
        (["Java", "Spring"], "entry"),
        (["Data Science", "Machine Learning"], "senior")
    ]
    
    for skills, level in test_cases:
        print(f"\n🔍 Testing: {skills} ({level} level)")
        jobs = generate_mock_jobs(skills, level)
        
        print(f"   Found {len(jobs)} jobs")
        
        # Check first job
        if jobs:
            job = jobs[0]
            print(f"   Sample job: {job['title']} at {job['company']}")
            print(f"   Apply URL: {job.get('apply_url', 'Missing!')}")
            print(f"   Location: {job.get('location', 'Missing!')}")
            print(f"   Salary: {job.get('salary', 'Missing!')}")
            
            # Verify required fields
            required_fields = ['title', 'company', 'apply_url', 'location', 'salary']
            missing_fields = [field for field in required_fields if not job.get(field)]
            
            if missing_fields:
                print(f"   ❌ Missing fields: {missing_fields}")
            else:
                print(f"   ✅ All required fields present")
    
    print("\n🎉 Job generation test completed!")

def test_session_state_simulation():
    """Simulate session state behavior"""
    print("\n🧪 Testing Session State Simulation...")
    print("=" * 50)
    
    # Simulate session state
    class MockSessionState:
        def __init__(self):
            self.session_id = None
            self.resume_data = None
    
    session_state = MockSessionState()
    
    # Test 1: No data
    print("Test 1: No session data")
    print(f"   Session ID: {session_state.session_id}")
    print(f"   Resume data: {session_state.resume_data}")
    print(f"   Has resume data: {session_state.resume_data is not None}")
    
    # Test 2: Set demo data
    print("\nTest 2: Setting demo data")
    session_state.session_id = "demo-123"
    session_state.resume_data = {
        "name": "Demo User",
        "Technical Skills": ["Python", "JavaScript", "React"],
        "experience": ["Software Engineer at TechCorp"],
        "education": ["BS Computer Science"]
    }
    
    print(f"   Session ID: {session_state.session_id}")
    print(f"   Resume data: {session_state.resume_data is not None}")
    print(f"   Skills: {session_state.resume_data.get('Technical Skills', [])}")
    
    # Test job generation with this data
    if session_state.resume_data and session_state.resume_data.get('Technical Skills'):
        skills = session_state.resume_data['Technical Skills']
        print(f"\n   Testing job generation with skills: {skills}")
        
        from agents.job import generate_mock_jobs
        jobs = generate_mock_jobs(skills, "mid")
        print(f"   Generated {len(jobs)} jobs")
        
        if jobs:
            job = jobs[0]
            print(f"   First job: {job['title']} at {job['company']}")
            print(f"   Apply URL: {job.get('apply_url')}")
    
    print("\n✅ Session state simulation completed!")

if __name__ == "__main__":
    test_job_generation()
    test_session_state_simulation()
    
    print("\n" + "=" * 50)
    print("🚀 All tests completed!")
    print("The Streamlit app should work correctly now.")
    print("Run: streamlit run app.py")
