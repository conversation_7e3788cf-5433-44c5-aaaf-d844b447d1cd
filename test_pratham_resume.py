#!/usr/bin/env python3
"""
Test resume parsing specifically for <PERSON><PERSON><PERSON>'s resume format
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pratham_resume_format():
    """Test parsing with <PERSON><PERSON><PERSON>'s resume format"""
    print("🧪 Testing Pratham's Resume Format...")
    print("=" * 50)
    
    from utils.file_parser import extract_fields
    
    # Sample resume in <PERSON><PERSON><PERSON>'s format
    sample_resume = """
Pratham Dixit
<EMAIL>
+91 6307803699

WORK EXPERIENCE:
Software Engineer at TechCorp (2022-Present)
- Developed web applications using React and Node.js
- Worked with Python and Django for backend services
- Collaborated with cross-functional teams

Junior Developer at StartupXYZ (2021-2022)
- Built REST APIs using Python and Flask
- Implemented database solutions with PostgreSQL

PROJECTS:
E-commerce Platform
- Built using React, Node.js, and MongoDB
- Implemented payment gateway integration
- Deployed on AWS with Docker

Task Management System
- Developed with Python Django and React
- Real-time updates using WebSockets
- User authentication and authorization

TECH STACK:
Python, JavaScript, React, Node.js, Django, Flask, MongoDB, PostgreSQL, 
AWS, Docker, Git, HTML, CSS, REST API, WebSockets

SOFT SKILLS:
Leadership, Team Collaboration, Problem Solving, Communication, 
Time Management, Adaptability, Critical Thinking

ACHIEVEMENTS:
- Winner of College Hackathon 2022
- Best Project Award in Software Engineering Course
- Dean's List for Academic Excellence (2021-2022)
- Published research paper on Machine Learning applications

POSITION OF RESPONSIBILITY:
Technical Lead - College Programming Club (2021-2022)
- Led a team of 15 students in organizing coding competitions
- Mentored junior students in programming concepts

Event Coordinator - Tech Fest 2022
- Organized technical events with 500+ participants
- Managed budget and logistics for multiple events
"""
    
    # Test the extraction
    result = extract_fields(sample_resume)
    
    print("📋 Extracted Data:")
    print(f"   Name: {result.get('name', 'NOT FOUND')}")
    print(f"   Email: {result.get('email', 'NOT FOUND')}")
    print(f"   Phone: {result.get('phone', 'NOT FOUND')}")
    print(f"   GitHub: {result.get('github', 'NOT FOUND')}")
    print(f"   LinkedIn: {result.get('linkedin', 'NOT FOUND')}")
    
    print(f"\n📊 Sections Extracted:")
    print(f"   Technical Skills ({len(result.get('Technical Skills', []))}): {result.get('Technical Skills', [])[:5]}...")
    print(f"   Work Experience ({len(result.get('work_experience', []))}): {result.get('work_experience', [])[:2]}...")
    print(f"   Projects ({len(result.get('projects', []))}): {result.get('projects', [])[:2]}...")
    print(f"   Soft Skills ({len(result.get('soft_skills', []))}): {result.get('soft_skills', [])[:3]}...")
    print(f"   Achievements ({len(result.get('achievements', []))}): {result.get('achievements', [])[:2]}...")
    print(f"   Position of Responsibility ({len(result.get('position_of_responsibility', []))}): {result.get('position_of_responsibility', [])[:1]}...")
    
    # Validation check
    from utils.data_validator import ResumeDataValidator
    is_valid, issues, cleaned_data = ResumeDataValidator.validate_parsed_resume(result)
    
    print(f"\n✅ Validation Results:")
    print(f"   Valid: {is_valid}")
    print(f"   Quality Score: {cleaned_data.get('data_quality_score', 0)}/100")
    print(f"   Issues: {issues if issues else 'None'}")
    
    # Check specific requirements
    required_found = {
        'name': bool(result.get('name')),
        'email': bool(result.get('email')),
        'phone': bool(result.get('phone')),
        'technical_skills': bool(result.get('Technical Skills')),
        'work_experience': bool(result.get('work_experience')),
        'projects': bool(result.get('projects')),
        'soft_skills': bool(result.get('soft_skills')),
        'achievements': bool(result.get('achievements')),
        'position_of_responsibility': bool(result.get('position_of_responsibility'))
    }
    
    print(f"\n📋 Required Fields Check:")
    for field, found in required_found.items():
        status = "✅" if found else "❌"
        print(f"   {status} {field.replace('_', ' ').title()}: {found}")
    
    success_rate = sum(required_found.values()) / len(required_found) * 100
    print(f"\n📊 Extraction Success Rate: {success_rate:.1f}%")
    
    return success_rate >= 80  # 80% success rate threshold

def test_manager_agent_with_pratham_format():
    """Test manager agent with Pratham's resume format"""
    print("\n🧪 Testing Manager Agent with Pratham's Format...")
    print("=" * 50)
    
    from agents.manager import manager_agent
    
    # Pratham's resume content
    resume_content = """
Pratham Dixit
Software Developer
<EMAIL>
+91 6307803699

WORK EXPERIENCE:
Full Stack Developer at InnovaTech Solutions (2022-Present)
- Developed responsive web applications using React and Node.js
- Built RESTful APIs with Python Django and FastAPI
- Implemented database solutions with PostgreSQL and MongoDB
- Deployed applications on AWS using Docker and Kubernetes
- Collaborated with cross-functional teams in Agile environment

Software Engineer Intern at TechStartup (2021-2022)
- Worked on frontend development using React and JavaScript
- Assisted in backend development with Python Flask
- Participated in code reviews and testing processes

PROJECTS:
E-commerce Platform
- Full-stack web application using React, Node.js, and MongoDB
- Implemented user authentication, shopping cart, and payment integration
- Deployed on AWS with CI/CD pipeline using GitHub Actions

Social Media Dashboard
- Built with Python Django and React
- Real-time data visualization using Chart.js
- User management and analytics features

Task Management System
- Developed using MERN stack (MongoDB, Express, React, Node.js)
- Real-time updates using Socket.io
- Role-based access control and project collaboration features

TECH STACK:
Python, JavaScript, TypeScript, React, Vue.js, Node.js, Express, Django, 
FastAPI, Flask, MongoDB, PostgreSQL, MySQL, Redis, AWS, Azure, Docker, 
Kubernetes, Git, GitHub, HTML5, CSS3, Bootstrap, Tailwind CSS, REST API, 
GraphQL, WebSockets, Jest, Pytest

SOFT SKILLS:
Leadership, Team Collaboration, Problem Solving, Communication, 
Time Management, Adaptability, Critical Thinking, Mentoring, 
Project Management, Analytical Thinking

ACHIEVEMENTS:
- Winner of National Level Hackathon 2023 - "Best Innovation Award"
- First Prize in College Technical Symposium 2022
- Dean's List for Academic Excellence (2021-2023)
- Published research paper on "AI in Web Development" - IEEE Conference 2023
- Scholarship recipient for Outstanding Academic Performance
- Certified AWS Solutions Architect Associate

POSITION OF RESPONSIBILITY:
Technical Lead - College Programming Society (2022-2023)
- Led a team of 20+ students in organizing coding competitions and workshops
- Mentored 50+ junior students in programming and web development
- Organized technical events with 1000+ participants

Event Coordinator - Annual Tech Fest 2022
- Managed technical events with budget of ₹5 lakhs
- Coordinated with 15+ companies for sponsorships and partnerships
- Successfully organized events with 2000+ participants

Student Representative - Computer Science Department (2021-2022)
- Represented student concerns and feedback to faculty
- Organized academic workshops and career guidance sessions
"""
    
    try:
        result = manager_agent.process_resume_upload(
            file_name="pratham_dixit_resume.txt",
            file_content=resume_content.encode('utf-8'),
            user_name="Pratham Dixit",
            email="<EMAIL>"
        )
        
        print("📋 Manager Agent Results:")
        print(f"   Success: {'error' not in result}")
        print(f"   Session ID: {result.get('session_id', 'Missing')}")
        print(f"   Validation Status: {result.get('validation_status', 'Missing')}")
        print(f"   Data Quality Score: {result.get('data_quality_score', 'Missing')}")
        
        # Check parsed data
        parsed_data = result.get("parsed", {})
        if parsed_data:
            print(f"\n📄 Parsed Data Quality:")
            print(f"   Name: {parsed_data.get('name', 'Missing')}")
            print(f"   Email: {parsed_data.get('email', 'Missing')}")
            print(f"   Phone: {parsed_data.get('phone', 'Missing')}")
            print(f"   Technical Skills: {len(parsed_data.get('Technical Skills', []))} found")
            print(f"   Work Experience: {len(parsed_data.get('work_experience', []))} entries")
            print(f"   Projects: {len(parsed_data.get('projects', []))} entries")
            print(f"   Achievements: {len(parsed_data.get('achievements', []))} entries")
            print(f"   Positions: {len(parsed_data.get('position_of_responsibility', []))} entries")
            
            # Show sample data
            if parsed_data.get('Technical Skills'):
                print(f"   Sample Skills: {', '.join(parsed_data['Technical Skills'][:5])}")
            
            return True
        else:
            print("❌ No parsed data returned")
            return False
            
    except Exception as e:
        print(f"❌ Manager agent test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Resume Parsing for Pratham's Format...")
    print("=" * 60)
    
    test1_result = test_pratham_resume_format()
    test2_result = test_manager_agent_with_pratham_format()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   File Parser: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Manager Agent: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 All tests passed!")
        print("✅ Resume parsing now correctly extracts all sections")
        print("✅ All required fields are being captured")
        print("✅ Data validation ensures quality")
        print("\n🚀 Ready to process Pratham's resume correctly!")
    else:
        print("\n⚠️ Some tests failed. Check the output above.")
