"""
Resume Improvement Agent
Provides AI-powered suggestions to enhance resume content and structure
"""

import os
from typing import Dict, List, Any
from langchain_google_genai import ChatGoogleGenerative<PERSON><PERSON>
from prompts.improver_prompt import IMPROVER_PROMPT

# Initialize Google Gemini
try:
    llm = ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        google_api_key=os.getenv("GOOGLE_API_KEY"),
        temperature=0.3
    )
    print("✅ Google AI model initialized successfully for improver")
except Exception as e:
    print(f"Warning: Google AI model initialization failed: {e}")
    llm = None

def analyze_resume_for_improvement(parsed_resume: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze resume and provide improvement suggestions
    """
    print("🔍 Analyzing resume for improvement opportunities...")
    
    if not llm:
        return {
            "suggestions": ["AI model not available for analysis"],
            "score": 70,
            "areas": ["Technical setup required"]
        }
    
    try:
        # Extract key resume components
        name = parsed_resume.get('name', 'Unknown')
        skills = parsed_resume.get('Technical Skills', [])
        experience = parsed_resume.get('experience', [])
        education = parsed_resume.get('education', [])
        projects = parsed_resume.get('projects', [])
        
        # Create analysis context
        resume_context = f"""
        RESUME ANALYSIS FOR: {name}
        
        TECHNICAL SKILLS: {', '.join(skills[:10]) if skills else 'None listed'}
        
        EXPERIENCE:
        {format_experience_for_analysis(experience)}
        
        EDUCATION:
        {format_education_for_analysis(education)}
        
        PROJECTS:
        {format_projects_for_analysis(projects)}
        
        TOTAL EXPERIENCE: {parsed_resume.get('total_years_experience', 0)} years
        EXPERIENCE LEVEL: {parsed_resume.get('experience_level', 'Entry')}
        """
        
        # Get improvement suggestions from LLM
        response = llm.invoke(IMPROVER_PROMPT.format(resume_context=resume_context))
        
        # Parse the response
        suggestions = parse_improvement_response(response.content)
        
        print(f"✅ Generated {len(suggestions.get('suggestions', []))} improvement suggestions")
        
        return suggestions
        
    except Exception as e:
        print(f"❌ Error analyzing resume: {e}")
        return {
            "suggestions": [f"Analysis error: {str(e)}"],
            "score": 60,
            "areas": ["Error in analysis"]
        }

def format_experience_for_analysis(experience: List[Dict]) -> str:
    """Format experience for analysis"""
    if not experience:
        return "No work experience listed"
    
    formatted = []
    for exp in experience[:3]:  # Top 3 experiences
        title = exp.get('title', 'Unknown Role')
        company = exp.get('company', 'Unknown Company')
        duration = exp.get('duration', 'Unknown Duration')
        description = exp.get('description', 'No description')
        
        formatted.append(f"- {title} at {company} ({duration}): {description[:100]}...")
    
    return '\n'.join(formatted)

def format_education_for_analysis(education: List[Dict]) -> str:
    """Format education for analysis"""
    if not education:
        return "No education listed"
    
    formatted = []
    for edu in education[:2]:  # Top 2 education entries
        degree = edu.get('degree', 'Unknown Degree')
        institution = edu.get('institution', 'Unknown Institution')
        year = edu.get('year', 'Unknown Year')
        
        formatted.append(f"- {degree} from {institution} ({year})")
    
    return '\n'.join(formatted)

def format_projects_for_analysis(projects: List[Dict]) -> str:
    """Format projects for analysis"""
    if not projects:
        return "No projects listed"
    
    formatted = []
    for proj in projects[:3]:  # Top 3 projects
        name = proj.get('name', 'Unknown Project')
        description = proj.get('description', 'No description')
        technologies = proj.get('technologies', [])
        
        tech_str = ', '.join(technologies[:5]) if technologies else 'No technologies listed'
        formatted.append(f"- {name}: {description[:80]}... (Tech: {tech_str})")
    
    return '\n'.join(formatted)

def parse_improvement_response(response: str) -> Dict[str, Any]:
    """Parse LLM response into structured improvement suggestions"""
    try:
        # Extract suggestions (look for numbered lists or bullet points)
        suggestions = []
        score = 75  # Default score
        areas = []
        
        lines = response.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Look for score
            if 'score' in line.lower() and any(char.isdigit() for char in line):
                import re
                score_match = re.search(r'(\d+)', line)
                if score_match:
                    score = int(score_match.group(1))
            
            # Look for suggestions
            if line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '•')):
                suggestion = line.lstrip('1234567890.-• ').strip()
                if len(suggestion) > 10:  # Valid suggestion
                    suggestions.append(suggestion)
            
            # Look for improvement areas
            if 'area' in line.lower() and ':' in line:
                area = line.split(':', 1)[1].strip()
                if area:
                    areas.append(area)
        
        # Ensure we have at least some suggestions
        if not suggestions:
            suggestions = [
                "Add more quantifiable achievements with numbers and metrics",
                "Include relevant keywords for your target industry",
                "Improve project descriptions with technical details",
                "Add soft skills and leadership examples",
                "Optimize formatting and structure for ATS systems"
            ]
        
        if not areas:
            areas = ["Content Enhancement", "Technical Skills", "Experience Description", "Formatting"]
        
        return {
            "suggestions": suggestions[:8],  # Top 8 suggestions
            "score": min(max(score, 0), 100),  # Ensure score is 0-100
            "areas": areas[:5],  # Top 5 areas
            "analysis_complete": True
        }
        
    except Exception as e:
        print(f"Error parsing improvement response: {e}")
        return {
            "suggestions": ["Error parsing improvement suggestions"],
            "score": 70,
            "areas": ["Analysis Error"]
        }

def improve_resume_node(state):
    """LangGraph node for resume improvement"""
    if "email" not in state["history"]:
        state["history"]["email"] = "<EMAIL>"
    
    improvements = analyze_resume_for_improvement(state["parsed"])
    state["improvements"] = improvements
    return state

# Export the node for LangGraph
from langchain_core.tools import tool

@tool
def ResumeImprover(parsed_resume: dict) -> dict:
    """
    Analyze resume and provide improvement suggestions
    """
    return analyze_resume_for_improvement(parsed_resume)
