from langchain.agents import Tool
from prompts.improver_prompt import PROMPT


def improve_resume(parsed_data):
    # Dummy improvement logic for now
    suggestions = [
        "Add metrics to your job descriptions.",
        "Include more recent projects.",
        "Mention GitHub or portfolio link."
    ]
    return suggestions

resume_improver_tool = Tool(
    name="ResumeImprover",
    func=improve_resume,
    description="Suggest improvements based on parsed resume"
)

def improver_node(state):
    improvement = improve_resume(state["parsed"])
    state["improvement"] = improvement
    return state