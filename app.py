import streamlit as st
import requests
import uuid
import json
from utils.data_validator import validate_and_clean_data, ResumeDataValidator

# Page configuration
st.set_page_config(
    page_title="Resume Intelligence Assistant",
    page_icon="📄",
    layout="wide"
)

# Initialize session state
if 'session_id' not in st.session_state:
    st.session_state.session_id = None
if 'resume_data' not in st.session_state:
    st.session_state.resume_data = None

# Data integrity check function
def check_data_integrity():
    """Check and ensure data integrity in session state"""
    if st.session_state.resume_data:
        # Validate current session data
        is_valid, issues, cleaned_data = ResumeDataValidator.validate_parsed_resume(st.session_state.resume_data)

        if not is_valid:
            st.warning(f"⚠️ Session data integrity issues detected: {', '.join(issues)}")
            # Auto-fix if possible
            if cleaned_data:
                st.session_state.resume_data = cleaned_data
                st.info("✅ Data automatically cleaned and validated")
            else:
                st.error("❌ Data corruption detected. Please re-upload your resume.")
                st.session_state.resume_data = None
                st.session_state.session_id = None

        return is_valid
    return True

# API Configuration
API_BASE_URL = "http://localhost:8001"

# Helper functions
def make_api_request(method, endpoint, **kwargs):
    """Make API request without authentication"""
    try:
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 30

        if method.upper() == 'POST':
            response = requests.post(f"{API_BASE_URL}{endpoint}", **kwargs)
        elif method.upper() == 'GET':
            response = requests.get(f"{API_BASE_URL}{endpoint}", **kwargs)
        else:
            return None

        return response
    except Exception as e:
        st.error(f"API Error: {str(e)}")
        return None

def safe_json_response(response):
    """Safely extract JSON from response"""
    if response is None:
        return None
    try:
        return response.json()
    except:
        return None

# Initialize session state
if 'session_id' not in st.session_state:
    st.session_state.session_id = None
if 'resume_data' not in st.session_state:
    st.session_state.resume_data = None

# Main app without authentication
st.title("🤖 Resume Intelligence Assistant")
st.markdown("*Your AI-powered career companion - No login required!*")

# Sidebar for file upload
with st.sidebar:
    st.header("📄 Upload Resume")
    uploaded_file = st.file_uploader(
        "Choose your resume file",
        type=['pdf', 'docx'],
        help="Upload your resume in PDF or DOCX format"
    )

    if st.button("Process Resume") and uploaded_file:
        with st.spinner("Processing your resume..."):
            try:
                # Prepare file for upload
                files = {"file": (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}

                # Try to call the API first
                response = make_api_request("POST", "/upload_resume_simple", files=files)

                if response and hasattr(response, 'status_code') and response.status_code == 200:
                    # API call successful
                    result = safe_json_response(response)

                    # Show API response for debugging if needed
                    if st.checkbox("Show API Response Debug"):
                        st.json(result)

                    if result and result.get("session_id"):
                        st.session_state.session_id = result["session_id"]

                        # Get parsed data
                        parsed_data = result.get("parsed", {})

                        if parsed_data and len(parsed_data) > 0:
                            # Validate and clean the parsed data
                            is_valid, issues, cleaned_data = ResumeDataValidator.validate_parsed_resume(parsed_data)

                            if is_valid:
                                # Store the validated data
                                st.session_state.resume_data = cleaned_data

                                st.success("✅ Resume processed and validated successfully!")
                                st.info(f"Session ID: {result['session_id'][:8]}...")
                                st.info(f"Data Quality Score: {cleaned_data.get('data_quality_score', 'N/A')}/100")

                                # Show extracted information
                                with st.expander("📄 Extracted Information"):
                                    st.write(f"**Name:** {cleaned_data.get('name', 'Not found')}")
                                    st.write(f"**Email:** {cleaned_data.get('email', 'Not found')}")
                                    st.write(f"**Phone:** {cleaned_data.get('phone', 'Not found')}")
                                    skills = cleaned_data.get('Technical Skills', [])
                                    st.write(f"**Skills ({len(skills)}):** {', '.join(skills[:5])}{' ...' if len(skills) > 5 else ''}")

                                    # Show validation issues if any
                                    if issues:
                                        st.warning(f"Validation notes: {', '.join(issues)}")

                                # Show stored data for debugging if needed
                                if st.checkbox("Show Stored Data Debug"):
                                    st.json(st.session_state.resume_data)
                            else:
                                st.warning("⚠️ Data validation failed. Using fallback data...")
                                st.session_state.resume_data = ResumeDataValidator.create_fallback_data(uploaded_file.name)
                                st.error(f"Validation issues: {', '.join(issues)}")

                        else:
                            # No parsed data, create fallback
                            st.warning("⚠️ API returned no parsed data. Creating fallback data...")
                            st.session_state.resume_data = {
                                "name": uploaded_file.name.split('.')[0].replace('_', ' ').title(),
                                "email": "<EMAIL>",
                                "Technical Skills": ["Python", "JavaScript", "React", "Node.js"],
                                "experience": ["Software Engineer"],
                                "education": ["Computer Science Degree"]
                            }
                            st.success("✅ Fallback resume data created!")

                        st.rerun()
                    else:
                        st.error("❌ Invalid response from server - no session ID")
                else:
                    # API failed, try direct processing
                    st.warning("⚠️ API not available, processing directly...")

                    try:
                        # Use manager agent directly
                        from agents.manager import manager_agent

                        # Read file content
                        file_content = uploaded_file.getvalue()

                        # Process with manager agent
                        result = manager_agent.process_resume_upload(
                            file_name=uploaded_file.name,
                            file_content=file_content,
                            user_name="Demo User",
                            email="<EMAIL>"
                        )

                        if "error" not in result and result.get("parsed"):
                            # Validate and clean the parsed data
                            parsed_data = result["parsed"]
                            is_valid, issues, cleaned_data = ResumeDataValidator.validate_parsed_resume(parsed_data)

                            if is_valid:
                                st.session_state.session_id = result["session_id"]
                                st.session_state.resume_data = cleaned_data

                                st.success("✅ Resume processed and validated successfully (direct mode)!")
                                st.info(f"Session ID: {result['session_id'][:8]}...")
                                st.info(f"Data Quality Score: {cleaned_data.get('data_quality_score', 'N/A')}/100")

                                # Show extracted information
                                with st.expander("📄 Extracted Information"):
                                    st.write(f"**Name:** {cleaned_data.get('name', 'Not found')}")
                                    st.write(f"**Email:** {cleaned_data.get('email', 'Not found')}")
                                    st.write(f"**Phone:** {cleaned_data.get('phone', 'Not found')}")
                                    skills = cleaned_data.get('Technical Skills', [])
                                    st.write(f"**Skills ({len(skills)}):** {', '.join(skills[:5])}{' ...' if len(skills) > 5 else ''}")

                                    if issues:
                                        st.warning(f"Validation notes: {', '.join(issues)}")
                            else:
                                st.warning("⚠️ Direct processing validation failed. Using fallback data...")
                                st.session_state.session_id = str(uuid.uuid4())
                                st.session_state.resume_data = ResumeDataValidator.create_fallback_data(uploaded_file.name)
                                st.error(f"Validation issues: {', '.join(issues)}")
                        else:
                            # Fallback to demo data
                            st.warning("Direct processing failed. Creating demo data...")
                            st.session_state.session_id = str(uuid.uuid4())
                            st.session_state.resume_data = {
                                "name": uploaded_file.name.split('.')[0].replace('_', ' ').title(),
                                "email": "<EMAIL>",
                                "phone": "******-0123",
                                "Technical Skills": ["Python", "JavaScript", "React", "Node.js", "AWS"],
                                "experience": ["Software Engineer at TechCorp"],
                                "education": ["BS Computer Science"]
                            }
                            st.success("✅ Demo resume data created!")

                    except Exception as e:
                        st.error(f"Direct processing failed: {e}")
                        # Final fallback
                        st.session_state.session_id = str(uuid.uuid4())
                        st.session_state.resume_data = {
                            "name": "Demo User",
                            "email": "<EMAIL>",
                            "Technical Skills": ["Python", "JavaScript", "React"],
                            "experience": ["Software Engineer"],
                            "education": ["Computer Science"]
                        }
                        st.success("✅ Fallback demo data created!")

                    st.rerun()

            except Exception as e:
                st.error(f"Error processing resume: {str(e)}")
                # Fallback to demo mode
                st.session_state.session_id = str(uuid.uuid4())
                st.session_state.resume_data = {"name": "Demo User", "Technical Skills": ["Python", "JavaScript"]}
                st.info("Running in demo mode due to error")
                st.rerun()

    # Show current session info
    if st.session_state.session_id:
        st.success(f"✅ Active Session")
        st.write(f"ID: {st.session_state.session_id[:8]}...")
        if st.session_state.resume_data and st.session_state.resume_data.get('name'):
            st.write(f"**User:** {st.session_state.resume_data['name']}")

    st.markdown("---")
    st.subheader("👤 Session Info")
    st.write("**Status:** No login required")
    st.write("**Mode:** Resume processing enabled")

# Main content area
if not st.session_state.session_id:
    st.info("👈 Please upload your resume from the sidebar to get started.")

    # Show demo information
    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("📄 Resume Analysis")
        st.write("Upload your resume and get detailed parsing with extracted skills, experience, and contact information.")

    with col2:
        st.subheader("🛠 Improvement Suggestions")
        st.write("Get AI-powered suggestions to enhance your resume and make it more attractive to employers.")

    with col3:
        st.subheader("💼 Job Recommendations")
        st.write("Find relevant job openings based on your skills and experience using advanced search algorithms.")

else:
    # Check data integrity before proceeding
    data_integrity_ok = check_data_integrity()

    if data_integrity_ok:
        # Action buttons interface for active session
        st.header(f"🤖 Resume Intelligence Assistant")
        st.subheader("What would you like me to help you with?")
    else:
        st.error("❌ Data integrity check failed. Please re-upload your resume.")
        st.stop()

    # Action buttons in a grid
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔍 Find Job Recommendations", use_container_width=True):
            with st.spinner("Finding job recommendations..."):

                # Show session state for debugging if needed
                if st.checkbox("Show Session Debug Info"):
                    st.write(f"Session ID: {st.session_state.session_id}")
                    st.write(f"Resume data exists: {st.session_state.resume_data is not None}")
                    if st.session_state.resume_data:
                        st.write(f"Resume data keys: {list(st.session_state.resume_data.keys())}")
                        st.json(st.session_state.resume_data)

                # Get job recommendations based on resume data
                if st.session_state.resume_data and st.session_state.resume_data.get('Technical Skills'):
                    tech_skills = st.session_state.resume_data['Technical Skills']
                    primary_skill = tech_skills[0] if tech_skills else "Software"

                    # Determine experience level from resume data
                    experience_level = st.session_state.resume_data.get('experience_level', 'Entry')
                    total_experience = st.session_state.resume_data.get('total_years_experience', 0)

                    st.success(f"✅ Using skills from resume: {tech_skills[:3]}...")
                    st.info(f"📊 Experience Level: {experience_level} ({total_experience} years)")
                else:
                    primary_skill = "Python"  # Default
                    tech_skills = [primary_skill, "Machine Learning", "Deep Learning"]
                    experience_level = "Entry"
                    st.warning("⚠️ No resume data found. Using default skills for demo.")

                    # Offer to load demo data
                    if st.button("🔄 Load Demo Resume Data"):
                        st.session_state.resume_data = {
                            "name": "Demo User",
                            "Technical Skills": ["Python", "Machine Learning", "JavaScript", "React"],
                            "experience_level": "Entry",
                            "total_years_experience": 1,
                            "experience": [{"title": "Software Engineer", "company": "Tech Corp"}]
                        }
                        st.success("Demo resume data loaded!")
                        st.rerun()

                # Generate India-focused job recommendations
                from agents.job import generate_india_focused_mock_jobs
                demo_jobs = generate_india_focused_mock_jobs(tech_skills, experience_level)

                # Validate job data
                jobs_valid, job_issues, validated_jobs = ResumeDataValidator.validate_job_data(demo_jobs)

                if jobs_valid and validated_jobs:
                    st.subheader("💼 Job Recommendations")
                    st.success(f"✅ Found {len(validated_jobs)} validated job opportunities for {primary_skill} developers")

                    # Show validation summary
                    jobs_with_apply = sum(1 for job in validated_jobs if job.get('apply_url'))
                    st.info(f"📊 Data Quality: {jobs_with_apply}/{len(validated_jobs)} jobs have apply links")

                    if job_issues:
                        with st.expander("⚠️ Job Validation Notes"):
                            for issue in job_issues:
                                st.warning(issue)
                else:
                    st.error("❌ Job validation failed. No valid jobs found.")
                    validated_jobs = []

                for i, job in enumerate(validated_jobs, 1):
                    with st.container():
                        # Job header with title and company
                        col1, col2, col3 = st.columns([2, 1, 1])

                        with col1:
                            st.markdown(f"**{i}. {job['title']}**")
                            st.write(f"🏢 {job['company']} | 🏷️ {job.get('company_type', 'Tech Company')}")
                            st.write(f"� {job['location']} | 💰 {job['salary']}")
                            if job.get('job_id') and job.get('posted_date'):
                                st.write(f"🆔 {job['job_id']} | 📅 {job['posted_date']}")

                        with col2:
                            # Relevance score
                            score = job['relevance_score']
                            color = "green" if score >= 80 else "orange" if score >= 60 else "red"
                            st.markdown(f"<div style='text-align: center; color: {color}; font-weight: bold;'>Match: {score}%</div>", unsafe_allow_html=True)

                        with col3:
                            # Apply button with direct link
                            apply_url = job.get('apply_url', job.get('url', '#'))
                            st.markdown(f"""
                                <a href="{apply_url}" target="_blank" style="text-decoration: none;">
                                    <button style="
                                        background-color: #4CAF50;
                                        color: white;
                                        padding: 10px 20px;
                                        border: none;
                                        border-radius: 5px;
                                        cursor: pointer;
                                        width: 100%;
                                        font-weight: bold;
                                    ">🚀 Apply Now</button>
                                </a>
                            """, unsafe_allow_html=True)

                        # Job description
                        st.write(f"📝 {job['content']}")

                        # Apply links section
                        col_apply1, col_apply2 = st.columns(2)
                        with col_apply1:
                            apply_url = job.get('apply_url', job.get('url', '#'))
                            st.markdown(f"[🎯 **Apply Directly**]({apply_url})")
                        with col_apply2:
                            st.markdown(f"[🔗 View Job Details]({job.get('url', '#')})")

                        st.markdown("---")

        if st.button("📊 Show Resume Data", use_container_width=True):
            with st.spinner("Loading resume data..."):
                st.subheader("📄 Your Resume Data")

                if st.session_state.resume_data:
                    st.json(st.session_state.resume_data)
                else:
                    st.warning("No resume data available. Please upload and process a resume first.")

                    # Try to provide fallback demo data
                    if st.button("🔄 Load Demo Data"):
                        st.session_state.resume_data = {
                            "name": "Demo User",
                            "email": "<EMAIL>",
                            "Technical Skills": ["Python", "JavaScript", "React", "Node.js"],
                            "experience": [
                                {"title": "Software Engineer", "company": "Tech Corp", "duration": "2 years"}
                            ],
                            "education": [
                                {"degree": "Computer Science", "school": "University"}
                            ]
                        }
                        st.success("Demo data loaded!")
                        st.rerun()

    with col2:
        if st.button("📈 Improve My Resume", use_container_width=True):
            with st.spinner("Analyzing your resume..."):
                st.subheader("🛠 Improvement Suggestions")
                improvements = [
                    "Add more quantifiable achievements with specific metrics",
                    "Include relevant certifications (AWS, Google Cloud)",
                    "Expand the technical skills section with frameworks",
                    "Add a professional summary at the top",
                    "Include links to GitHub and portfolio projects"
                ]
                for improvement in improvements:
                    st.write(f"• {improvement}")

        if st.button("❓ Get Help", use_container_width=True):
            with st.spinner("Loading help information..."):
                st.info("🚀 Welcome to Resume Intelligence Assistant! I can help you with:\n\n🔍 **Job Recommendations** - Find relevant positions based on your skills\n\n📈 **Resume Improvements** - Get suggestions to enhance your resume\n\n📊 **Resume Analysis** - View parsed data from your uploaded resume\n\nSimply click any of the action buttons to get started!")

    # Show session statistics
    st.markdown("---")
    st.subheader("📊 Session Information")
    col1, col2 = st.columns(2)
    with col1:
        st.write(f"**Mode:** No Authentication Required")
        st.write(f"**Session:** {st.session_state.session_id[:8]}...")
    with col2:
        st.write(f"**Status:** Resume Uploaded ✅")
        if st.session_state.resume_data and st.session_state.resume_data.get('name'):
            st.write(f"**User:** {st.session_state.resume_data['name']}")
        else:
            st.write(f"**Data:** Processing enabled")

# Footer
st.markdown("---")
st.markdown("*Resume Intelligence Assistant - Powered by AI (Demo Mode - No Authentication Required)*")
